# Currency Handling in Gravity Forms with Opayo and GeoIP

## Payment Currency Configuration in Opayo Plugin

The payment currency in the Opayo (formerly SagePay) plugin for Gravity Forms is handled as follows:

1. The currency is determined in `class-gf-sagepay-form.php`:
```php
// Current Currency
$currency = GFCommon::get_currency();
```

2. This currency is then sent to Opayo in the payment request:
```php
$sagepay_arg['Currency'] = $currency;
```

3. The currency handling works with the Multi-Currency for Gravity Forms plugin, which allows:
   - Setting a base currency for each form
   - Auto-currency selection based on visitor's location
   - Processing payments in either the user's selected currency or the form's base currency

4. The currency flow is:
   - Gravity Forms provides the base currency through `GFCommon::get_currency()`
   - If the Multi-Currency plugin is active, it may modify this based on form settings
   - The final currency value is sent to Opayo in the `Currency` parameter

## YellowTree GeoIP Plugin Integration

The YellowTree GeoIP plugin affects currency settings through:

1. A currency filter in the theme's `functions.php`:
```php
add_filter('gform_currency', 'geoip_currency');
function geoip_currency($currency) {
    $data = geoip_detect2_get_info_from_current_ip()->raw;
    return $data['extra']['currency_code'];
}
```

2. This filter:
   - Gets the visitor's location using `geoip_detect2_get_info_from_current_ip()`
   - Extracts the appropriate currency code from the GeoIP data
   - Overrides Gravity Forms' default currency with the visitor's local currency

3. Additional JavaScript functionality in `form-geo.js`:
   - Detects visitor's country using `geoip_detect.get_info()`
   - Sets country code in Gravity Forms with `.geoip_country` field
   - Affects address information sent to Opayo

4. Important considerations:
   - Overrides default currency in Gravity Forms
   - Affects all Gravity Forms on the site
   - Currency sent to Opayo will be based on visitor's location
   - Ensure Opayo supports all possible detected currencies

## Limiting Currencies to GBP and USD

You can limit available currencies in the Gravity Forms Multi-Currency plugin through:

1. **Currency Field Settings**:
   - Add a Currency field to your form
   - Select "Specific Currencies" instead of "All currencies"
   - Check only GBP and USD in the currency list

2. **Form Settings**:
   - Navigate to form settings
   - Find "Multi Currency Settings" section
   - Set "Form Base Currency" to either GBP or USD
   - Optionally disable auto-currency selection
   - Set "Process Payment in" to "Form's base currency"

Implementation steps:
1. Open form editor
2. Add/edit Currency field
3. In field settings:
   - Select "Specific Currencies"
   - Check only:
     - British Pound Sterling (GBP)
     - United States Dollar (USD)
4. Save form

This configuration will:
- Limit currency options to GBP and USD
- Override GeoIP-based currency selection
- Ensure payments only process in these currencies 