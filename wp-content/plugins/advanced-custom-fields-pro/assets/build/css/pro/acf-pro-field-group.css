/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./src/advanced-custom-fields-pro/assets/src/sass/pro/acf-pro-field-group.scss ***!
  \******************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/*--------------------------------------------------------------------------------------------
*
*	Vars
*
*--------------------------------------------------------------------------------------------*/
/* colors */
/* acf-field */
/* responsive */
/*--------------------------------------------------------------------------------------------
*
*	ACF 6 ↓
*
*--------------------------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------------------------
*
*  Mixins
*
*--------------------------------------------------------------------------------------------*/
/*---------------------------------------------------------------------------------------------
*
*	Flexible Content
*
*---------------------------------------------------------------------------------------------*/
.acf-field-setting-fc_layout .acf-toggle-fc-layout {
  width: 34px;
  height: 31px;
  margin: 0;
  padding: 0;
  border: 0;
  background: transparent;
  cursor: pointer;
  left: 20.83%;
  right: 20.83%;
  top: 33.33%;
  bottom: 33.33%;
}
.acf-field-setting-fc_layout .toggle-indicator::before {
  z-index: -1;
  content: "";
  display: inline-flex;
  width: 20px;
  height: 20px;
  margin-left: -28px;
  background-color: currentColor;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  -webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);
  mask-image: url(../../../images/icons/icon-chevron-down.svg);
}
.rtl .acf-field-setting-fc_layout .toggle-indicator::before {
  margin-left: 0px;
  position: absolute;
  top: 9px;
  z-index: 100;
  left: 8px;
}

.acf-field-setting-fc_layout .toggle-indicator.open::before {
  -webkit-mask-image: url(../../../images/icons/icon-chevron-up.svg);
  mask-image: url(../../../images/icons/icon-chevron-up.svg);
}
.acf-field-setting-fc_layout .toggle-indicator.closed::before {
  -webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);
  mask-image: url(../../../images/icons/icon-chevron-down.svg);
}
.acf-field-setting-fc_layout .acf-flexible-content-field-label-name {
  padding-left: 5px;
}
.acf-field-setting-fc_layout .acf-fc-meta {
  margin: 0 0 10px;
  padding: 0;
}
.acf-field-setting-fc_layout .acf-fc-meta li {
  margin: 0 0 10px;
  padding: 0;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-display {
  float: left;
  width: 100%;
  padding-right: 5px;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-left {
  width: calc(50% - 4px);
  float: left;
  clear: left;
  margin-right: 4px;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-right {
  width: calc(50% - 4px);
  float: left;
  margin-left: 4px;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-min {
  width: calc(25% - 5px);
  float: left;
  margin-right: 5px;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-max {
  width: calc(25% - 10px);
  float: left;
  margin-left: 4px;
}
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-label .acf-input-prepend,
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-name .acf-input-prepend,
.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-display .acf-input-prepend {
  min-width: 60px;
}
.acf-field-setting-fc_layout .acf-fc_draggable,
.acf-field-setting-fc_layout .reorder-layout {
  cursor: grab;
}
.acf-field-setting-fc_layout .acf-fl-actions a {
  padding: 1px 0;
  font-size: 13px;
  line-height: 20px;
}

/*---------------------------------------------------------------------------------------------
*
*	Clone
*
*---------------------------------------------------------------------------------------------*/
.acf-field-object-clone {
  /* group */
  /* seamless */
}
.acf-field-object-clone[data-display=seamless] .acf-field-setting-instructions,
.acf-field-object-clone[data-display=seamless] .acf-field-setting-layout,
.acf-field-object-clone[data-display=seamless] .acf-field-setting-wrapper,
.acf-field-object-clone[data-display=seamless] .acf-field-setting-conditional_logic {
  display: none;
}

/*----------------------------------------------------------------------------
*
*  Pro fields with inactive licenses.
*
*----------------------------------------------------------------------------*/
.acf-pro-inactive-license .acf-pro-field-object .li-field-label:before {
  -webkit-mask-image: url("../../../images/icons/icon-lock.svg") !important;
  mask-image: url("../../../images/icons/icon-lock.svg") !important;
  pointer-events: none;
}
.acf-pro-inactive-license .acf-pro-field-object .edit-field {
  pointer-events: none;
  color: #667085;
}
.acf-pro-inactive-license .acf-pro-field-object .row-options {
  display: none;
}

/*# sourceMappingURL=acf-pro-field-group.css.map*/