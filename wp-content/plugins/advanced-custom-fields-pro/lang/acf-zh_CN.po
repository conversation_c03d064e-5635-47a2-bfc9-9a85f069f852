# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-06-27T14:24:00+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/ajax/class-acf-ajax-upgrade.php:24
msgid "Sorry, you don't have permission to do that."
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:37
msgid "Sorry, you are not allowed to do that."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:27
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: includes/class-acf-site-health.php:643
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:683
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr "当类型设置为 media_library 时，%s 需要有效的附件 ID。"

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:667
msgid "%s is a required property of acf."
msgstr "%s 是 acf 的必需属性。"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "The value of icon to save."
msgstr "要保存的图标的值。"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "The type of icon to save."
msgstr "要保存的图标的类型。"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Yes icon"
msgstr "是的图标"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Wordpress-alt icon"
msgstr "Wordpress-alt 图标"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Wordpress icon"
msgstr "Wordpress 图标"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Welcome write-blog icon"
msgstr "欢迎写博客图标"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Welcome widgets-menus icon"
msgstr "欢迎小部件菜单图标"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Welcome view-site icon"
msgstr "欢迎查看站点图标"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Welcome learn-more icon"
msgstr "欢迎了解更多图标"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Welcome comments icon"
msgstr "欢迎评论图标"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Welcome add-page icon"
msgstr "欢迎添加页面图标"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Warning icon"
msgstr "警告图标"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Visibility icon"
msgstr "可见性图标"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Video-alt3 icon"
msgstr "Video-alt3 图标"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Video-alt2 icon"
msgstr "Video-alt2 图标"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Video-alt icon"
msgstr "Video-alt 图标"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Vault icon"
msgstr "Vault 图标"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Upload icon"
msgstr "上传图标"

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Update icon"
msgstr "更新图标"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Unlock icon"
msgstr "解锁图标"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Universal access alternative icon"
msgstr "通用访问替代图标"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Universal access icon"
msgstr "通用访问图标"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Undo icon"
msgstr "撤消图标"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Twitter icon"
msgstr "Twitter 图标"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Trash icon"
msgstr "回收站图标"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Translation icon"
msgstr "翻译图标"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Tickets alternative icon"
msgstr "Tickets alternative 图标"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Tickets icon"
msgstr "Tickets 图标"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Thumbs-up icon"
msgstr "点赞图标"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Thumbs-down icon"
msgstr "点踩图标"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Text icon"
msgstr "文本图标"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Testimonial icon"
msgstr "感言图标"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Tagcloud icon"
msgstr "标签云图标"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Tag icon"
msgstr "标签图标"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Tablet icon"
msgstr "平板电脑图标"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Store icon"
msgstr "商店图标"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Sticky icon"
msgstr "置顶图标"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Star-half icon"
msgstr "半星图标"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Star-filled icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Star-empty icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Sos icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Sort icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Smiley icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Smartphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Slides icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Shield-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Shield icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Share-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Share-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Share icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Search icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Screenoptions icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Schedule icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Rss icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Redo icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Randomize icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Products icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Pressthis icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Post-status icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Portfolio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Plus-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Plus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Playlist-video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Playlist-audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Phone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Performance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paperclip icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Palmtree icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "No alternative icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "No icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Networking icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Nametag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Move icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Money icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Minus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Migrate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Microphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Menu icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Megaphone icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Media video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Media text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Media spreadsheet icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Media interactive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Media document icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Media default icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Media code icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Media audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Media archive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Marker icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Lock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Location-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Location icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "List-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Lightbulb icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Leftright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Layout icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Laptop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Info icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Index-card icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Images-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Images-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Image rotate-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Image rotate-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Image rotate icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Image flip-vertical icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Image flip-horizontal icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Image filter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Image crop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Id-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Id icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Hidden icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Heart icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Hammer icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Groups icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Grid-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Googleplus icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forms icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Format video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Format status icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Format quote icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Format image icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Format gallery icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Format chat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Format audio icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Format aside icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Flag icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Filter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Feedback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Facebook alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Facebook icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "External icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Exerpt-view icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Email alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Email icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Video icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Unlink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Underline icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Ul icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Textcolor icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Table icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Strikethrough icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Spellcheck icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Rtl icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Removeformatting icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Quote icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Paste word icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Paste text icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Paragraph icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Outdent icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Ol icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Kitchensink icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Justify icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Italic icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Insertmore icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Indent icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Help icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Expand icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Customchar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Contract icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Code icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Break icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bold icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "alignright icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "alignleft icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "aligncenter icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Edit icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Download icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Dismiss icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Desktop icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Dashboard icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Controls volumeon icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Controls volumeoff icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Controls skipforward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Controls skipback icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Controls repeat icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Controls play icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Controls pause icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Controls forward icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Controls back icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Cloud icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Clock icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Clipboard icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Chart pie icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Chart line icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Chart bar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Chart area icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Category icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Cart icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Carrot icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Camera icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Calendar alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Calendar icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Businessman icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Building icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Book alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Book icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Backup icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Awards icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Art icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow up-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow up-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow up icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Arrow right-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Arrow right-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Arrow right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Arrow left-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Arrow left-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Arrow left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Arrow down-alt2 icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Arrow down-alt icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
msgid "Arrow down icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Archive icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:407
msgid "Analytics icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Align-right icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Align-none icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Align-left icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Align-center icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Album icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Users icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Tools icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Customizer icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Comments icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:387
msgid "Collapse icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Appearance icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Generic icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: includes/class-acf-site-health.php:704
msgid "JSON Load Paths"
msgstr ""

#: includes/class-acf-site-health.php:698
msgid "JSON Save Paths"
msgstr ""

#: includes/class-acf-site-health.php:689
msgid "Registered ACF Forms"
msgstr ""

#: includes/class-acf-site-health.php:683
msgid "Shortcode Enabled"
msgstr ""

#: includes/class-acf-site-health.php:675
msgid "Field Settings Tabs Enabled"
msgstr ""

#: includes/class-acf-site-health.php:667
msgid "Field Type Modal Enabled"
msgstr ""

#: includes/class-acf-site-health.php:659
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:650
msgid "Block Preloading Enabled"
msgstr ""

#: includes/class-acf-site-health.php:638
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:633
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:606
msgid "Registered ACF Blocks"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Light"
msgstr ""

#: includes/class-acf-site-health.php:600
msgid "Standard"
msgstr ""

#: includes/class-acf-site-health.php:599
msgid "REST API Format"
msgstr ""

#: includes/class-acf-site-health.php:591
msgid "Registered Options Pages (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:572
msgid "Registered Options Pages (UI)"
msgstr ""

#: includes/class-acf-site-health.php:542
msgid "Options Pages UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:534
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:522
msgid "Registered Taxonomies (UI)"
msgstr ""

#: includes/class-acf-site-health.php:510
msgid "Registered Post Types (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:498
msgid "Registered Post Types (UI)"
msgstr ""

#: includes/class-acf-site-health.php:485
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: includes/class-acf-site-health.php:478
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:473
msgid "Number of Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:440
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: includes/class-acf-site-health.php:427
msgid "Field Groups Enabled for REST API"
msgstr ""

#: includes/class-acf-site-health.php:415
msgid "Registered Field Groups (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:403
msgid "Registered Field Groups (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:391
msgid "Registered Field Groups (UI)"
msgstr ""

#: includes/class-acf-site-health.php:379
msgid "Active Plugins"
msgstr ""

#: includes/class-acf-site-health.php:353
msgid "Parent Theme"
msgstr ""

#: includes/class-acf-site-health.php:342
msgid "Active Theme"
msgstr ""

#: includes/class-acf-site-health.php:333
msgid "Is Multisite"
msgstr ""

#: includes/class-acf-site-health.php:328
msgid "MySQL Version"
msgstr ""

#: includes/class-acf-site-health.php:323
msgid "WordPress Version"
msgstr ""

#: includes/class-acf-site-health.php:316
msgid "Subscription Expiry Date"
msgstr ""

#: includes/class-acf-site-health.php:308
msgid "License Status"
msgstr ""

#: includes/class-acf-site-health.php:303
msgid "License Type"
msgstr ""

#: includes/class-acf-site-health.php:298
msgid "Licensed URL"
msgstr ""

#: includes/class-acf-site-health.php:292
msgid "License Activated"
msgstr ""

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr ""

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr ""

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr ""

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373 assets/build/js/acf-input.js:11311
#: assets/build/js/acf-input.js:12393
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
#: assets/build/js/acf-input.js:1460 assets/build/js/acf-input.js:1558
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
#: assets/build/js/acf-input.js:1437 assets/build/js/acf-input.js:1534
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
#: assets/build/js/acf-input.js:1412 assets/build/js/acf-input.js:1507
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
#: assets/build/js/acf-input.js:1387 assets/build/js/acf-input.js:1481
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
#: assets/build/js/acf-input.js:1368 assets/build/js/acf-input.js:1461
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
#: assets/build/js/acf-input.js:1349 assets/build/js/acf-input.js:1441
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
#: assets/build/js/acf-input.js:1052 assets/build/js/acf-input.js:1116
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
#: assets/build/js/acf-input.js:1029 assets/build/js/acf-input.js:1092
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
#: assets/build/js/acf-input.js:1003 assets/build/js/acf-input.js:1064
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
#: assets/build/js/acf-input.js:976 assets/build/js/acf-input.js:1035
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
#: assets/build/js/acf-input.js:957 assets/build/js/acf-input.js:1015
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
#: assets/build/js/acf-input.js:938 assets/build/js/acf-input.js:995
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
#: assets/build/js/acf-input.js:915 assets/build/js/acf-input.js:971
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
#: assets/build/js/acf-input.js:892 assets/build/js/acf-input.js:947
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
#: assets/build/js/acf-input.js:865 assets/build/js/acf-input.js:918
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
#: assets/build/js/acf-input.js:838 assets/build/js/acf-input.js:889
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
#: assets/build/js/acf-input.js:819 assets/build/js/acf-input.js:869
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
#: assets/build/js/acf-input.js:800 assets/build/js/acf-input.js:849
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
#: assets/build/js/acf-input.js:1188 assets/build/js/acf-input.js:1259
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
#: assets/build/js/acf-input.js:1165 assets/build/js/acf-input.js:1235
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
#: assets/build/js/acf-input.js:1326 assets/build/js/acf-input.js:1415
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
#: assets/build/js/acf-input.js:1303 assets/build/js/acf-input.js:1389
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
#: assets/build/js/acf-input.js:1276 assets/build/js/acf-input.js:1358
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-input.js:1249 assets/build/js/acf-input.js:1327
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
#: assets/build/js/acf-input.js:1230 assets/build/js/acf-input.js:1305
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
#: assets/build/js/acf-input.js:1211 assets/build/js/acf-input.js:1283
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1139 assets/build/js/acf-input.js:1207
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1113 assets/build/js/acf-input.js:1180
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1094 assets/build/js/acf-input.js:1160
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1075 assets/build/js/acf-input.js:1140
msgid "Relationship is equal to"
msgstr ""

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF 字段"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO 功能"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "续订 PRO 即可解锁"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "更新 PRO 许可证"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "如果没有有效许可证，则无法编辑 PRO 字段。"

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr "请激活您的 ACF PRO 许可证以编辑分配给 ACF 块的字段组。"

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "请激活您的 ACF PRO 许可证才能编辑此选项页面。"

#: includes/api/api-template.php:381 includes/api/api-template.php:435
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"仅当 format_value 也为 true 时，才可以返回转义的 HTML 值。为了安全起见，字段"
"值尚未返回。"

#: includes/api/api-template.php:46 includes/api/api-template.php:247
#: includes/api/api-template.php:939
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"仅当 format_value 也为 true 时，才可以返回转义的 HTML 值。为了安全起见，该字"
"段值尚未返回。"

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "请联系您的网站管理员或开发人员了解更多详细信息。"

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "了解更多"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "隐藏详情"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "显示详情"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - 通过 %3$s 呈现"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "续订 ACF PRO 许可证"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "更新许可证"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "管理许可证"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "块编辑器不支持“高”位置"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "升级到 ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF<a href=\"%s\" target=\"_blank\">选项页</a>是通过字段管理全局设置的自定义"
"管理页。您可以创建多个页面和子页面。"

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "添加选项页面"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "在编辑器中用作标题的占位符。"

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "标题占位符"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4个月免费"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr " （复制自 %s）"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "选择选项页面"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "克隆分类法"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "创建分类法"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "克隆文章类型"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "创建文章类型"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "链接字段组"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "添加字段"

#: includes/admin/post-types/admin-field-group.php:147
#: assets/build/js/acf-field-group.js:2803
#: assets/build/js/acf-field-group.js:3298
msgid "This Field"
msgstr "这个字段"

#: includes/admin/admin.php:352
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:350
msgid "Feedback"
msgstr "反馈"

#: includes/admin/admin.php:348
msgid "Support"
msgstr "支持"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:323
msgid "is developed and maintained by"
msgstr "开发和维护者"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "将此 %s 添加到所选字段组的位置规则中。"

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"启用双向设置允许您更新为此字段选择的每个值的目标字段中的值，添加或删除正在更"
"新的项目的文章 ID、分类法 ID 或用户 ID。有关更多信息，请阅读<a href=\"%s\" "
"target=\"_blank\">文档</a>。"

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"选择字段以将引用存储回正在更新的项目。您可以选择该字段。目标字段必须与该字段"
"的显示位置兼容。例如，如果此字段显示在分类法上，则您的目标字段应为分类法类型"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "目标字段"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "更新所选值的字段，引用回此 ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "双向"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s 字段"

#: includes/fields/class-acf-field-page_link.php:487
#: includes/fields/class-acf-field-post_object.php:400
#: includes/fields/class-acf-field-select.php:380
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "选择多个"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "WP Engine logo"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "仅小写字母、下划线和破折号，最多 32 个字符。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr "用于分配此分类的术语的功能名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr "分配分类项能力"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr "用于删除该分类法的分类项的功能名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr "删除分类项功能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr "用于编辑该分类法的分类项的功能名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr "编辑分类项能力"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr "用于管理该分类法的分类项的功能名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr "管理分类项能力"

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr "设置帖子是否应从搜索结果和分类存档页面中排除。"

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP Engine 的更多工具"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "由 %s 团队专为使用 WordPress 构建的用户而构建"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "查看定价和升级"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "了解更多"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"利用 ACF 块和选项页面等功能以及循环、弹性内容、克隆和图库等复杂的字段类型，加"
"快您的工作流程并开发更好的网站。"

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "使用 ACF PRO 解锁高级功能并构建更多功能"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s 字段"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "无分类项"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "无文章类型"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "无文章"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "无分类"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "无字段分组"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "无字段"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "无描述"

#: includes/fields/class-acf-field-page_link.php:454
#: includes/fields/class-acf-field-post_object.php:363
#: includes/fields/class-acf-field-relationship.php:562
msgid "Any post status"
msgstr "任何文章状态"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"这个分类标准的关键字已经被ACF以外注册的另一个分类标准所使用，不能使用。"

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr "此分类法已被 ACF 中的另一个分类法使用，因此无法使用。"

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr "分类法只能包含小写字母数字字符、下划线或破折号。"

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "分类法必须少于 32 个字符。"

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "回收站中未找到分类法"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "未找到分类法"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "搜索分类法"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "查看分类法"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "新分类法"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "编辑分类法"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "新增分类法"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "在回收站中找不到文章类型"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "找不到文章类型"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "搜索文章类型"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "查看文章类型"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "新文章类型"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "编辑文章类型"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "新增文章类型"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr "此帖子类型密钥已被 ACF 外部注册的另一个帖子类型使用，因此无法使用。"

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr "此帖子类型密钥已被 ACF 中的另一个帖子类型使用，无法使用。"

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr "此字段不得是 WordPress <a href=\"%s\" target=\"_blank\">保留词</a>。"

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr "帖子类型键只能包含小写字母数字字符、下划线或破折号。"

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "帖子类型键必须少于 20 个字符。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "我们不建议在 ACF 块中使用此字段。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"显示 WordPress 可视化编辑器，如文章和页面中所示，可提供丰富的文本编辑体验，还"
"支持多媒体内容。"

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "可视化编辑器"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr "允许选择一个或多个可用于在数据对象之间创建关系的用户。"

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "专门为存储网址而设计的文本输入。"

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"允许您选择值 1 或 0（打开或关闭、true 或 false 等）的切换开关。可以呈现为程式"
"化的开关或复选框。"

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr "用于选择时间的交互式 UI。可以使用字段设置自定义时间格式。"

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "用于存储文本段落的基本文本区域输入。"

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "基本文本输入，可用于存储单个字符串值。"

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr "允许根据字段设置中指定的条件和选项选择一个或多个分类术语。"

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"允许您将字段分组到编辑屏幕中的选项卡部分。对于保持字段的组织性和结构化很有"
"用。"

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr "包含您指定的选项的下拉列表。"

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"双栏界面，用于选择一个或多个帖子、页面或自定义帖子类型项目，以创建与您当前正"
"在编辑的项目的关系。包括搜索和过滤选项。"

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr "用于使用范围滑块元素选择指定范围内的数值的输入。"

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr "一组单选按钮输入，允许用户从您指定的值中进行单一选择。"

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"一种交互式且可定制的用户界面，用于选择一个或多个帖子、页面或帖子类型项目，并"
"提供搜索选项。 "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "使用屏蔽字段提供密码的输入。"

#: includes/fields/class-acf-field-page_link.php:446
#: includes/fields/class-acf-field-post_object.php:355
#: includes/fields/class-acf-field-relationship.php:554
msgid "Filter by Post Status"
msgstr "按文章状态过滤"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"交互式下拉菜单，用于选择一个或多个帖子、页面、自定义帖子类型项目或存档 URL，"
"并提供搜索选项。"

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"一个交互式组件，用于通过使用本机 WordPress oEmbed 功能来嵌入视频、图像、推"
"文、音频和其他内容。"

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "输入仅限于数值。"

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"用于与其他字段一起向编辑者显示消息。对于提供有关您的字段的附加上下文或说明很"
"有用。"

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr "允许您使用 WordPress 本机链接选择器指定链接及其属性，例如标题和目标。"

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr "使用本机 WordPress 媒体选择器上传或选择图像。"

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr "提供一种将字段组织为组的方法，以更好地组织数据和编辑屏幕。"

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"用于使用 Google 地图选择位置的交互式 UI。需要 Google 地图 API 密钥和其他配置"
"才能正确显示。"

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr "使用本机 WordPress 媒体选择器上传或选择文件。"

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "专门用于存储电子邮件地址的文本输入。"

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr "用于选择日期和时间的交互式 UI。可以使用字段设置自定义日期返回格式。"

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr "用于选择日期的交互式用户界面。可以使用字段设置自定义日期返回格式。"

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "用于选择颜色或指定十六进制值的交互式 UI。"

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr "一组复选框输入，允许用户选择您指定的一个或多个值。"

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr "一组具有您指定的值的按钮，用户可以从提供的值中选择一个选项。"

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"允许您将自定义字段分组并组织到可折叠面板中，这些面板在编辑内容时显示。对于保"
"持大型数据集整洁很有用。"

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"这提供了一种通过充当一组可以一次又一次重复的子字段的父字段来重复幻灯片、团队"
"成员和号召性用语图块等内容的解决方案。"

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"这提供了用于管理附件集合的交互式界面。大多数设置与图像字段类型类似。其他设置"
"允许您指定在库中添加新附件的位置以及允许的最小/最大附件数量。"

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"这提供了一个简单、结构化、基于布局的编辑器。弹性内容字段允许您通过使用布局和"
"子字段来设计可用块来定义、创建和管理具有完全控制的内容。"

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"这允许您选择并显示现有字段。它不会复制数据库中的任何字段，而是在运行时加载并"
"显示所选字段。克隆字段可以用所选字段替换自身，也可以将所选字段显示为一组子字"
"段。"

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "克隆"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "高级"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (新)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "原始"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "文章 ID 无效。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "选择进行审核的帖子类型无效。"

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "更多"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "教程"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "选择字段"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "尝试不同的搜索词或浏览 %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "热门字段"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "没有搜索到“%s”结果"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "搜索字段..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "选择字段类型"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "热门"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "添加分类法"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "创建自定义分类法对文章类型内容进行分类"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "添加您的第一个分类法"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "分层分类法可以有后代（如类别）。"

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "使分类在前端和管理仪表板中可见。"

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "可以使用此分类法对一种或多种文章类型进行分类。"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "类型"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "类型"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "类型"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr "使用可选的自定义控制器来代替“WP_REST_Terms_Controller”。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr "在 REST API 中公开此文章类型。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr "自定义查询变量名"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr "可以使用非漂亮的永久链接访问术语，例如 {query_var}={term_slug}。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "分层分类法 URL 中的父子术语。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr "自定义 URL 中使用的 slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr "此分类的永久链接已禁用。"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr "使用分类键作为 slug 重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "分类法键"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr "选择用于此分类的永久链接类型。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "在帖子类型列表屏幕上显示分类法列。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr "显示管理栏"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "在快速/批量编辑面板中显示分类。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr "快速编辑"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "列出标签云小部件控件中的分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr "标签云"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr "要调用的 PHP 函数名称，用于清理从元框中保存的分类数据。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr "Meta Box 清理回调"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr "要调用的 PHP 函数名称来处理分类法上元框的内容。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr "注册元框回调"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr "无  Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr "自定义  Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"控制内容编辑器屏幕上的元框。默认情况下，对于分层分类法显示“类别”元框，对于非"
"分层分类法显示“标签”元框。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr "Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr "分类 Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr "标签  Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "指向标签的链接"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "描述块编辑器中使用的导航链接块变体。"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "%s 的链接"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "标签链接"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr "为块编辑器中使用的导航链接块变体分配标题。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← 转到标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr "在更新分类项后，指定用于链接回主索引的文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "返回项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← 前往 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "标签列表"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "将文本分配给表格隐藏标题。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "标签列表导航"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "将文本分配给表格分页隐藏标题。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "按类别过滤"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "将文本分配给帖子列表中的过滤器按钮。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "按项目过滤"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "按 %s 过滤"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr "默认情况下描述不突出；但是，某些主题可能会显示它。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "描述编辑标签屏幕上的描述字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "描述 字段 描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"指定父分类项以创建层次结构。例如，“爵士乐”一词就是“Bebop”和“Big Band”的父词"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "描述编辑标签屏幕上的父字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "父字段描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"“slug”是该名称的 URL 友好版本。它通常全部小写，仅包含字母、数字和连字符。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "描述编辑标签屏幕上的 Slug 字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Slug 字段描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "该名称是它在您网站上的显示方式"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "描述编辑标签屏幕上的名称字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "名称 字段 描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "无标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr "当没有可用标签或类别时，分配帖子和媒体列表中显示的文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "无分类项"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "无 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "未找到标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"当没有可用标签时，分配在分类元框中单击“从最常用的选择”文本时显示的文本，并在"
"没有分类项时分配术语列表中使用的文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "未找到"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "将文本分配给最常用选项卡的标题字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "最常被使用"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "从最常用的标签中选择"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"当禁用 JavaScript 时，指定元框中使用的“从最常用的选项中选择”文本。仅用于非层"
"次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "从最常用的中选择"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "从最常用的 %s 中选择"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "添加或删除标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"禁用 JavaScript 时分配在元框中使用的添加或删除项目文本。仅用于非层次分类法"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "添加或删除项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "添加或删除%s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "用逗号分隔标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr "使用分类元框中使用的逗号文本分配单独的项目。仅用于非层次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "用逗号分隔项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "用逗号分隔 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "热门标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "分配热门项目文本。仅用于非层次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "热门项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "热门 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "搜索标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "分配搜索项文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "父级分类："

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "分配父项文本，但在末尾添加冒号 (:)。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "带冒号的父项"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "父类别"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "分配父项文本。仅用于分层分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "父级项目"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "上级 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "新标签名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "分配新的项目名称文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "新项目名称"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "新 %s 名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "新增标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "分配新的项目名称文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "更新标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "分配更新项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "更新项目"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "更新 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "查看标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "在管理栏中可以在编辑期间查看分类项。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "编辑标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "编辑分类项时位于编辑器屏幕的顶部。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "所有标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "分配所有项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "分配菜单名称文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "菜单标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "活动分类法已启用并在 WordPress 中注册。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "分类法的描述性摘要。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "该分类项的描述性摘要。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "分类项说明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "单个字符串，不能有空格，允许下划线和破折号。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "分类项 Slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "默认分类项的名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "分类项名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr "为无法删除的分类法创建分类项。默认情况下不会选择它来发布帖子。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "默认分类项"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"此分类法中的分类项是否应按照提供给“wp_set_object_terms()”的顺序进行排序。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "排序分类项"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "添加文章类型"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr "使用自定义文章类型将 WordPress 的功能扩展到标准文章和页面之外。"

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "添加您的第一个文章类型"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "我知道我在做什么，告诉我所有的选择。"

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "高级配置"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "分层文章类型可以有后代（如页面）。"

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "分层"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "在前端和管理仪表板中可见。"

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "公开"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "仅限小写字母、下划线和破折号，最多 20 个字符。"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "单一标签"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "复数标签"

#: includes/admin/views/acf-post-type/advanced-settings.php:1298
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr "使用可选的自定义控制器来代替“WP_REST_Posts_Controller”。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1297
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr "控制器类"

#: includes/admin/views/acf-post-type/advanced-settings.php:1279
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr "REST API URL 的命名空间部分。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1278
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr "命名空间路由"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr "文章类型 REST API URL 的基本 URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr "基本网址"

#: includes/admin/views/acf-post-type/advanced-settings.php:1245
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr "在 REST API 中公开此帖子类型。需要使用块编辑器。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1244
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr "在 REST API 中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:1223
msgid "Customize the query variable name."
msgstr "自定义查询变量名称。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr "查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr "不支持查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr "自定义查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr "可以使用非漂亮的永久链接访问项目，例如。 {post_type}={post_slug}。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1195
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr "查询变量支持"

#: includes/admin/views/acf-post-type/advanced-settings.php:1170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr "可以使用查询字符串访问一个或多个项目的 URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1169
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr "可公开查询"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Custom slug for the Archive URL."
msgstr "存档 URL 的自定义 slug。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
msgid "Archive Slug"
msgstr "归档 Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1134
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr "拥有一个项目存档，可以使用主题中的存档模板文件进行自定义。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1133
msgid "Archive"
msgstr "归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:1113
msgid "Pagination support for the items URLs such as the archives."
msgstr "对项目 URL（例如归档）的分页支持。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1112
msgid "Pagination"
msgstr "分页"

#: includes/admin/views/acf-post-type/advanced-settings.php:1095
msgid "RSS feed URL for the post type items."
msgstr "文章类型项目的 RSS 源 URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1094
msgid "Feed URL"
msgstr "Feed 网址"

#: includes/admin/views/acf-post-type/advanced-settings.php:1076
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr "更改永久链接结构以将 `WP_Rewrite::$front` 前缀添加到 URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1075
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr "前面的 URL 前缀"

#: includes/admin/views/acf-post-type/advanced-settings.php:1056
msgid "Customize the slug used in the URL."
msgstr "自定义 URL 中使用的 slug。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1055
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr "URL Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
msgid "Permalinks for this post type are disabled."
msgstr "此文章类型的永久链接已禁用。"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1038
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr "使用下面输入中定义的自定义 slug 重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-post-type/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr "无固定链接（防止 URL 重写）"

#: includes/admin/views/acf-post-type/advanced-settings.php:1029
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr "自定义固定链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-post-type/advanced-settings.php:1198
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "文章类型键"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1026
#: includes/admin/views/acf-post-type/advanced-settings.php:1036
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr "使用文章类型键作为 slug 重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr "永久链接重写"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Delete items by a user when that user is deleted."
msgstr "删除用户后，删除该用户的项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Delete With User"
msgstr "与用户一起删除"

#: includes/admin/views/acf-post-type/advanced-settings.php:995
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "允许从“工具”>“导出”导出文章类型。"

#: includes/admin/views/acf-post-type/advanced-settings.php:994
msgid "Can Export"
msgstr "可以导出"

#: includes/admin/views/acf-post-type/advanced-settings.php:963
msgid "Optionally provide a plural to be used in capabilities."
msgstr "可以选择提供要在功能中使用的复数。"

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Plural Capability Name"
msgstr "复数能力名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid "Choose another post type to base the capabilities for this post type."
msgstr "选择另一个文章类型以基于此文章类型的功能。"

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Singular Capability Name"
msgstr "单一能力名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"默认情况下，文章类型的功能将继承“文章”功能名称，例如。编辑帖子、删除帖子。允"
"许使用文章类型特定的功能，例如。编辑_{单数}，删除_{复数}。"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Rename Capabilities"
msgstr "重命名功能"

#: includes/admin/views/acf-post-type/advanced-settings.php:913
msgid "Exclude From Search"
msgstr "从搜索中排除"

#: includes/admin/views/acf-post-type/advanced-settings.php:900
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr "允许将项目添加到“外观”>“菜单”屏幕中的菜单。必须在“屏幕选项”中打开。"

#: includes/admin/views/acf-post-type/advanced-settings.php:899
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr "外观菜单支持"

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "显示为管理栏中“新建”菜单中的一个项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Show In Admin Bar"
msgstr "在管理栏中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:849
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr "为编辑屏幕设置元框时要调用的 PHP 函数名称。"

#: includes/admin/views/acf-post-type/advanced-settings.php:848
msgid "Custom Meta Box Callback"
msgstr "自定义 Meta Box 回调"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
msgid "Menu Icon"
msgstr "菜单图标"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "管理仪表板侧边栏菜单中的位置。"

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "菜单位置"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"默认情况下，文章类型将在管理菜单中获得一个新的顶级项目。如果此处提供了现有的"
"顶级项目，则文章类型将作为其下的子菜单项添加。"

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "管理菜单父级"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "侧边栏菜单中的管理编辑器导航。"

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "在管理菜单中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "可以在管理仪表板中编辑和管理项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "在用户界面中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "文章的链接。"

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "导航链接块变体的描述。"

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "项目链接说明"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "%s 的链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "文章链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "导航链接块变体的标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "项目链接"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s 链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "文章已更新。"

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "项目更新后在编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "项目已更新。"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s 已更新。"

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "文章已计划。"

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "在安排项目后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "项目已计划"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s 已计划。"

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "文章已恢复为草稿。"

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "将项目恢复为草稿后在编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "项目恢复为草稿"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s 已恢复为草稿。"

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "文章已私密发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "在编辑发布私密项目后的通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "私密项目已发布。"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s 已私密发布"

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "文章已发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "在编辑发布项目后的通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "项目已发布"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s 已发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "文章列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr "由屏幕阅读器用于文章类型列表屏幕上的项目列表。"

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "项目列表"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s 列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "文章列表导航"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr "由屏幕阅读器用于文章类型列表屏幕上的过滤器列表分页。"

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "项目列表导航"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s 列表导航"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "按日期过滤文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr "屏幕阅读器用于按文章类型列表屏幕上的日期标题进行过滤。"

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "按日期过滤项目"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "按日期过滤 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "筛选文章列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr "由屏幕阅读器用于文章类型列表屏幕上的过滤器链接标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "筛选项目列表"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "筛选 %s 列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "在媒体模式中显示上传到此项目的所有媒体。"

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "已上传至此项目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "已上传至此 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "插入至文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "作为向内容添加媒体时的按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "插入到媒体按钮"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "插入至 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "用作特色图像"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "作为选择使用图像作为特色图像的按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "使用特色图像"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "移除特色图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "作为删除特色图像时的按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "移除特色图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "设置特色图像"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "作为设置特色图片时的按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "设置特色图像"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "特色图像"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "在编辑器中用于特色图像 meta box 的标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "特色图像 Meta Box"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "文章属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "在编辑器中用于文章属性 Meta Box 的标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "属性 Meta Box"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s 属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "文章归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"将带有此标签的“文章类型存档”项目添加到在启用存档的情况下将项目添加到 CPT 中的"
"现有菜单时显示的文章列表。仅当在“实时预览”模式下编辑菜单并且提供了自定义存档"
"段时才会出现。"

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "归档导航菜单"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s 归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "在回收站中未找到文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "当回收站中没有文章时，位于文章类型列表屏幕的顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "在回收站中未找到项目"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "在回收站中未找到 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "未找到文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr "当没有可显示的帖子时，位于文章类型列表屏幕的顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "未找到项目。"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "未找到 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "搜索文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "搜索项目时位于项目屏幕的顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "搜索项目"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "搜索 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "父页："

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "对于文章类型列表屏幕中的分层类型。"

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "父项前缀"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "父级 %s："

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "新文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "新项目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "新 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "新增文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "添加新项目时位于编辑器屏幕的顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "新增项目"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "新增 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "查看文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"显示在管理栏中的“所有文章”视图中，前提是文章类型支持存档并且主页不是该文章类"
"型的存档。"

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "查看项目"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "查看文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "在管理栏中编辑项目时可以查看项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "查看项目"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "查看 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "编辑文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "编辑项目时位于编辑器屏幕的顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "编辑项目"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "编辑 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "所有文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "在管理仪表板的文章类型子菜单中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "所有项目"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "所有 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "文章类型的管理菜单名称。"

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "菜单名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "使用单数和复数标签重新生成所有标签"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "重新生成"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "活动文章类型已在 WordPress 中启用并注册。"

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "文章类型的描述性摘要。"

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "添加自定义"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "启用内容编辑器中的各种功能。"

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "文章格式"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "编辑"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "引用通告"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "选择现有分类法对文章类型的项目进行分类。"

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "浏览字段"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "没有什么可导入的"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr "可以停用自定义文章类型 UI 插件。"

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "已从自定义文章类型 UI 导入 %d 项 -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "导入分类法失败。"

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "导入文章类型失败。"

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "自定义文章类型 UI 插件中没有选择导入。"

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "已导入 %s 个项目"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"导入与现有文章类型或分类法具有相同键的文章类型或分类法将会用导入的设置覆盖现"
"有文章类型或分类法的设置。"

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "从自定义文章类型 UI 导入"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""
"以下代码可用于注册所选项目的本地版本。在本地存储字段组、帖子类型或分类法可以"
"提供许多好处，例如更快的加载时间、版本控制和动态字段/设置。只需将以下代码复制"
"并粘贴到主题的functions.php 文件中或将其包含在外部文件中，然后从ACF 管理中停"
"用或删除这些项目。"

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "导出 - 生成 PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "导出"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "选择分类法"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "选择文章类型"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "已导出 %s 个项目。"

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr "分类"

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr "标签"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s 分类法已创建"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s 分类法已更新"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "分类法草稿已更新。"

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "分类法已计划。"

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "已提交分类法。"

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "分类法已保存。"

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "分类法已删除。"

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "分类法已更新。"

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"无法注册此分类法，因为其密钥已被另一个插件或主题注册的另一个分类法使用。"

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "%s 分类法已同步。"

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "%s 分类法已复制。"

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "%s 分类法已停用。"

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "%s 分类法已激活。"

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "分类项"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "%s 个文章类型已同步。"

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "%s 个文章类型已复制。"

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "%s 个文章类型已停用。"

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "%s 个文章类型已激活。"

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "文章类型"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "高级设置"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "基本设置"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"无法注册此帖子类型，因为其密钥已被另一个插件或主题注册的另一个帖子类型使用。"

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr "页面"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "链接现有字段组"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s 文章类型已创建"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "将字段添加到 %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s 文章类型已更新"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "文章类型草稿已更新。"

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "已计划文章类型。"

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "已提交文章类型。"

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "文章类型已保存。"

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "文章类型已更新。"

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "文章类型已删除。"

#: includes/admin/post-types/admin-field-group.php:146
#: assets/build/js/acf-field-group.js:1159
#: assets/build/js/acf-field-group.js:1383
msgid "Type to search..."
msgstr "输入以搜索……"

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1186
#: assets/build/js/acf-field-group.js:2349
#: assets/build/js/acf-field-group.js:1429
#: assets/build/js/acf-field-group.js:2761
msgid "PRO Only"
msgstr "仅限专业版"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr "字段组链接成功。"

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"导入使用自定义帖子类型 UI 注册的帖子类型和分类法并使用 ACF 进行管理。 <a "
"href=\"%s\">开始使用</a>。"

#: includes/admin/admin.php:46 includes/admin/admin.php:352
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "分类法"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "文章类型"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "完成"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "字段组"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "选择一个或多个字段组..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "请选择要链接的字段组。"

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "字段组链接成功。"

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "注册失败"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr "无法注册此项目，因为其密钥已被另一个插件或主题注册的另一个项目使用。"

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "权限"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "网址"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "可见性"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "标签"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "字段设置选项卡"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1015
msgid "[ACF shortcode value disabled for preview]"
msgstr "[预览时禁用 ACF 短代码值]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "关闭模态框"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1701
#: assets/build/js/acf-field-group.js:2032
msgid "Field moved to other group"
msgstr "字段移至其他组"

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1443 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr "关闭模态框"

#: includes/fields/class-acf-field-tab.php:119
msgid "Start a new group of tabs at this tab."
msgstr "在此选项卡上启动一组新选项卡。"

#: includes/fields/class-acf-field-tab.php:118
msgid "New Tab Group"
msgstr "新标签组"

#: includes/fields/class-acf-field-select.php:423
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "使用 select2 使用程式化复选框"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "保存其他选择"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "允许其他选择"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "添加 全部切换"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "保存自定义值"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "允许自定义值"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr "复选框自定义值不能为空。取消选中任何空值。"

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "更新"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "高级自定义字段 LOGO"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "保存更改"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "字段组标题"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "添加标题"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr "ACF 新手？请查看我们的<a href=\"%s\" target=\"_blank\">入门指南</a>。"

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "添加字段组"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF 使用<a href=\"%s\" target=\"_blank\">字段组</a>将自定义字段分组在一起，然"
"后将这些字段附加到编辑屏幕。"

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "添加您的第一个字段组"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "选项页"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF 块"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "画廊字段"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "弹性内容字段"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "循环字段"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "使用 ACF PRO 解锁额外功能"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "删除字段组"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "于 %1$s 在 %2$s 创建"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "群组设置"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "定位规则"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"从30多种字段类型中进行选择<a href=\"%s\"target=\"_blank\">了解更多信息</a>。"

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"开始为您的文章、页面、自定义帖子类型和其他WordPress内容创建新的自定义字段。"

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "添加您的第一个字段"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "添加字段"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "展示"

#: includes/fields.php:383
msgid "Validation"
msgstr "验证"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "常规"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "导入 JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "导出为 JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "%s 字段组已停用。"

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "%s 个字段组已激活。"

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "停用"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "停用此项目"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "激活"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "激活此项目"

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2862
#: assets/build/js/acf-field-group.js:3375
msgid "Move field group to trash?"
msgstr "将字段组移至回收站？"

#: acf.php:500 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "停用"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:558
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自"
"定义字段 PRO。"

#: acf.php:556
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自"
"定义字段。"

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - 我们检测到在 ACF 初始化之前检索 ACF 字段值的一次或多"
"次调用。不支持此操作，并且可能会导致数据格式错误或丢失。 <a href=\"%2$s\" "
"target=\"_blank\">了解如何解决此问题</a>。"

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s 必须拥有具有以下角色之一的用户：%2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s 必须具有有效的用户 ID。"

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "无效的请求。"

#: includes/fields/class-acf-field-select.php:637
msgid "%1$s is not one of %2$s"
msgstr "%1$s 不是 %2$s 之一。"

#: includes/fields/class-acf-field-post_object.php:649
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s 必须具有以下分类项之一：%2$s"

#: includes/fields/class-acf-field-post_object.php:633
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s 必须属于以下文章类型之一：%2$s"

#: includes/fields/class-acf-field-post_object.php:624
msgid "%1$s must have a valid post ID."
msgstr "%1$s 必须具有有效的文章 ID。"

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s 需要有效的附件 ID。"

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "在 REST API 中显示"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "启用透明度"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA 数组"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA 字符串"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "十六进制字符串"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "升级到专业版"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "启用"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "“%s”不是有效的电子邮件地址"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "颜色值"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "选择默认颜色"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "清除颜色"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "区块"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "选项"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "用户"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "菜单项"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "小工具"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "附件"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "分类法"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr "文章"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "最后更新：%s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "抱歉，这篇文章无法进行差异比较。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "无效的字段组参数。"

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "等待保存"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "已保存"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "导入"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "查看变更"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "位于：%s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "位于插件中：%s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "位于主题中：%s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "各种各样的"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "同步更改"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "加载差异"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "查看本地JSON更改"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "访问网站"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "查看详情"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "版本 %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "信息"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">服务台</a>。我们服务台上的支持专业人员将协助"
"您解决更深入的技术难题。"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">讨论</a>。我们的社区论坛上有一个活跃且友好的"
"社区，他们也许能够帮助您了解 ACF 世界的“操作方法”。"

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">文档</a>。我们详尽的文档包含您可能遇到的大多"
"数情况的参考和指南。"

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"我们热衷于支持，并希望您通过ACF充分利用自己的网站。如果遇到任何困难，可以在几"
"个地方找到帮助："

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "帮助和支持"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "如果您需要帮助，请使用“帮助和支持”选项卡进行联系。"

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"在创建您的第一个字段组之前，我们建议您先阅读<a href=\"%s\" target=\"_blank\">"
"入门</a>指南，以熟悉插件的原理和最佳实践。"

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields 插件提供了一个可视化的表单生成器，用于自定义带有额外字"
"段的WordPress编辑屏幕，以及一个直观的API，用于在任何主题模板文件中显示自定义"
"字段的值。"

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "概述"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "位置类型“%s”已被注册。"

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Class“%s”不存在。"

#: includes/ajax/class-acf-ajax-query-users.php:28
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "无效的随机数。"

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "加载字段时出错。"

#: assets/build/js/acf-input.js:3438 assets/build/js/acf-input.js:3507
#: assets/build/js/acf-input.js:3686 assets/build/js/acf-input.js:3760
msgid "Location not found: %s"
msgstr "找不到位置：%s"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>错误</strong>：%s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "小工具"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "用户角色"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "评论"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "文章格式"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "菜单项"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "文章状态"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "菜单"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "菜单位置"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "菜单"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "文章分类法"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "子页面（有父页面）"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "父页面（有子页）"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "顶级页面 (无父页面)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "文章页"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "首页"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "页面类型"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "查看后端"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "查看前端"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "登录"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "当前用户"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "页面模板"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "注册"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "添加 / 编辑"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "用户表单"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "父级页面"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "超级管理员"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "当前用户角色"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "默认模板"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "文章模板"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "文章类别"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "所有 %s 格式"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "附件"

#: includes/validation.php:313
msgid "%s value is required"
msgstr "%s 的值是必填项"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "显示此字段的条件"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "条件逻辑"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "与"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "本地 JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "克隆字段"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "还请检查所有高级扩展（%s）是否已更新到最新版本。"

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "此版本包含对数据库的改进，需要升级。"

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "感谢您更新到 %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "需要升级数据库"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "选项页面"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "画廊"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "弹性内容"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "循环"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "返回所有工具"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"如果多个字段组同时出现在编辑界面，会使用第一个字段组里的选项（就是序号最小的"
"那个字段组）"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>选择</b>需要在编辑界面<b>隐藏</b>的条目。"

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "隐藏元素"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "发送 Trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "标签"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "类别"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "页面属性"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "格式"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "作者"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "别名"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "修订"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "评论"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "讨论"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "摘要"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "内容编辑器"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "固定链接"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "在字段组列表中显示"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "序号小的字段组会排在最前面"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "序号"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "字段之下"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "标签之下"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "说明位置"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "标签位置"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "边栏"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "正常（内容之后）"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "高（标题之后）"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "位置"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "无缝（无 metabox）"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "标准（WP Metabox）"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "样式"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "类型"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "密钥"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "序号"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "关闭字段"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "宽度"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "包装属性"

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr "必填"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "说明"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "字段类型"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "单个字符串，不能有空格，允许下划线和破折号。"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "字段名称"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "在编辑界面显示的名字"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "字段标签"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "删除"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "删除字段"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "移动"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "把字段移动到其它群组"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "复制字段"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "编辑字段"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "拖拽排序"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2387
#: assets/build/js/acf-field-group.js:2812
msgid "Show this field group if"
msgstr "显示此字段组的条件"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "没有可用更新。"

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "数据库升级完成。<a href=\"%s\">查看新的部分 </a>。"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "阅读更新任务..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "升级失败。"

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "升级完成。"

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "升级数据到 %s 版本"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr "升级前最好先备份一下。确定现在升级吗？"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "请选择至少一个要升级的站点。"

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "数据库升级完成，<a href=\"%s\">返回网络面板</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "网站已是最新版"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "站点需要将数据库从 %1$s 升级到 %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "网站"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "升级站点"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr "下面的网站需要升级数据库，点击 %s 。"

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "添加规则组"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "创建一组规则以确定自定义字段在哪个编辑界面上显示"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "规则"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "复制"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "复制到剪贴板"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"选择您要导出的项目，然后选择导出方法。导出为 JSON 以导出到 .json 文件，然后可"
"以将其导入到另一个 ACF 安装中。生成 PHP 以导出到 PHP 代码，您可以将其放置在主"
"题中。"

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "选择字段组"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "没选择字段组"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "生成 PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "导出字段组"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "导入的文件是空白的"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "文本类型不对"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "文件上传失败，请重试"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"选择您要导入的高级自定义字段 JSON 文件。当您单击下面的导入按钮时，ACF 将导入"
"该文件中的项目。"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "导入字段组"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "同步"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "选择 %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "复制"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "复制此项"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "支持"

#: includes/admin/admin.php:346
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "文档"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "描述"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "有可用同步"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "%s 个字段组已同步。"

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s已复制字段组。"

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "启用 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "检查网站并升级"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "升级数据库"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "字段"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "移动字段"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "请选择这个字段的位置"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "现在可以在 %2$s 字段组中找到 %1$s 字段"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "移动完成。"

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "激活"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "字段 Keys"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "设置"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "位置"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:1688 assets/build/js/acf-input.js:1850
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1541
#: assets/build/js/acf-field-group.js:1860
msgid "copy"
msgstr "复制"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:627
#: assets/build/js/acf-field-group.js:782
msgid "(this field)"
msgstr "(这个字段)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:1629 assets/build/js/acf-input.js:1651
#: assets/build/js/acf-input.js:1783 assets/build/js/acf-input.js:1808
msgid "Checked"
msgstr "已选中"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1646
#: assets/build/js/acf-field-group.js:1972
msgid "Move Custom Field"
msgstr "移动自定义字段"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:653
#: assets/build/js/acf-field-group.js:808
msgid "No toggle fields available"
msgstr "没有可用的切换字段"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "字段组的标题是必填项"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1635
#: assets/build/js/acf-field-group.js:1958
msgid "This field cannot be moved until its changes have been saved"
msgstr "保存这个字段的修改以后才能移动这个字段"

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1445
#: assets/build/js/acf-field-group.js:1755
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" 这个字符串不能作为字段名字的开始部分"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "字段组草稿已更新。"

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "字段组已定时。"

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "字段组已提交。"

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "字段组已保存。"

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "字段组已发布。"

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "字段组已删除。"

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "字段组已更新。"

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "工具"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "不等于"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "等于"

#: includes/locations.php:104
msgid "Forms"
msgstr "表单"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "页面"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "文章"

#: includes/fields.php:328
msgid "Relational"
msgstr "关系"

#: includes/fields.php:327
msgid "Choice"
msgstr "选项"

#: includes/fields.php:325
msgid "Basic"
msgstr "基本"

#: includes/fields.php:276
msgid "Unknown"
msgstr "未知"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "字段类型不存在"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "检测到垃圾邮件"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "文章已更新"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "更新"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "验证邮箱"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "内容"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "标题"

#: includes/assets.php:376 includes/forms/form-comment.php:140
#: assets/build/js/acf-input.js:8413 assets/build/js/acf-input.js:9185
msgid "Edit field group"
msgstr "编辑字段组"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1815 assets/build/js/acf-input.js:1990
msgid "Selection is less than"
msgstr "选择小于"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1799 assets/build/js/acf-input.js:1965
msgid "Selection is greater than"
msgstr "选择大于"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1771 assets/build/js/acf-input.js:1936
msgid "Value is less than"
msgstr "值小于"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1744 assets/build/js/acf-input.js:1908
msgid "Value is greater than"
msgstr "值大于"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:1602 assets/build/js/acf-input.js:1744
msgid "Value contains"
msgstr "值包含"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:1579 assets/build/js/acf-input.js:1713
msgid "Value matches pattern"
msgstr "值匹配模式"

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:1560 assets/build/js/acf-input.js:1725
#: assets/build/js/acf-input.js:1693 assets/build/js/acf-input.js:1888
msgid "Value is not equal to"
msgstr "值不等于"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:1533 assets/build/js/acf-input.js:1669
#: assets/build/js/acf-input.js:1657 assets/build/js/acf-input.js:1828
msgid "Value is equal to"
msgstr "值等于"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:1514 assets/build/js/acf-input.js:1637
msgid "Has no value"
msgstr "没有价值"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:1487 assets/build/js/acf-input.js:1586
msgid "Has any value"
msgstr "有任何价值"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1570 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "退出"

#: includes/assets.php:350 assets/build/js/acf.js:1744
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "确定吗?"

#: includes/assets.php:370 assets/build/js/acf-input.js:10481
#: assets/build/js/acf-input.js:11531
msgid "%d fields require attention"
msgstr "%d 个字段需要注意"

#: includes/assets.php:369 assets/build/js/acf-input.js:10479
#: assets/build/js/acf-input.js:11529
msgid "1 field requires attention"
msgstr "1 个字段需要注意"

#: includes/assets.php:368 includes/validation.php:247
#: includes/validation.php:255 assets/build/js/acf-input.js:10474
#: assets/build/js/acf-input.js:11524
msgid "Validation failed"
msgstr "验证失败"

#: includes/assets.php:367 assets/build/js/acf-input.js:10642
#: assets/build/js/acf-input.js:11702
msgid "Validation successful"
msgstr "验证成功"

#: includes/media.php:54 assets/build/js/acf-input.js:8241
#: assets/build/js/acf-input.js:8989
msgid "Restricted"
msgstr "限制"

#: includes/media.php:53 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8753
msgid "Collapse Details"
msgstr "折叠"

#: includes/media.php:52 assets/build/js/acf-input.js:8056
#: assets/build/js/acf-input.js:8750
msgid "Expand Details"
msgstr "展开"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:7923
#: assets/build/js/acf-input.js:8598
msgid "Uploaded to this post"
msgstr "上传到这个文章"

#: includes/media.php:50 assets/build/js/acf-input.js:7962
#: assets/build/js/acf-input.js:8637
msgctxt "verb"
msgid "Update"
msgstr "更新"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "编辑"

#: includes/assets.php:364 assets/build/js/acf-input.js:10252
#: assets/build/js/acf-input.js:11296
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "如果浏览其它页面，会丢失当前所做的修改"

#: includes/api/api-helpers.php:2959
msgid "File type must be %s."
msgstr "字段类型必须是 %s。"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2956 assets/build/js/acf-field-group.js:781
#: assets/build/js/acf-field-group.js:2427
#: assets/build/js/acf-field-group.js:946
#: assets/build/js/acf-field-group.js:2859
msgid "or"
msgstr "或"

#: includes/api/api-helpers.php:2932
msgid "File size must not exceed %s."
msgstr "文件尺寸最大不能超过 %s。"

#: includes/api/api-helpers.php:2928
msgid "File size must be at least %s."
msgstr "文件尺寸至少得是 %s。"

#: includes/api/api-helpers.php:2915
msgid "Image height must not exceed %dpx."
msgstr "图像高度最大不能超过 %dpx。"

#: includes/api/api-helpers.php:2911
msgid "Image height must be at least %dpx."
msgstr "图像高度至少得是 %dpx。"

#: includes/api/api-helpers.php:2899
msgid "Image width must not exceed %dpx."
msgstr "图像宽度最大不能超过 %dpx。"

#: includes/api/api-helpers.php:2895
msgid "Image width must be at least %dpx."
msgstr "图像宽度至少得是 %dpx。"

#: includes/api/api-helpers.php:1409 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(无标题)"

#: includes/api/api-helpers.php:765
msgid "Full Size"
msgstr "原图"

#: includes/api/api-helpers.php:730
msgid "Large"
msgstr "大"

#: includes/api/api-helpers.php:729
msgid "Medium"
msgstr "中"

#: includes/api/api-helpers.php:728
msgid "Thumbnail"
msgstr "缩略图"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1090
#: assets/build/js/acf-field-group.js:1277
msgid "(no label)"
msgstr "(无标签)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "设置文本区域的高度"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "行"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "文本区域"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "添加一个可以全选的复选框"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "将 \"自定义\" 值保存到字段的选择中"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "允许添加 \"自定义\" 值"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "添加新选项"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "全选"

#: includes/fields/class-acf-field-page_link.php:476
msgid "Allow Archives URLs"
msgstr "允许存档 url"

#: includes/fields/class-acf-field-page_link.php:185
msgid "Archives"
msgstr "存档"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "页面链接"

#: includes/fields/class-acf-field-taxonomy.php:870
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "添加"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:840
msgid "Name"
msgstr "名称"

#: includes/fields/class-acf-field-taxonomy.php:825
msgid "%s added"
msgstr "%s 已添加"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "%s already exists"
msgstr "%s 已存在"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "User unable to add new %s"
msgstr "用户无法添加新的 %s"

#: includes/fields/class-acf-field-taxonomy.php:664
msgid "Term ID"
msgstr "内容ID"

#: includes/fields/class-acf-field-taxonomy.php:663
msgid "Term Object"
msgstr "对象缓存"

#: includes/fields/class-acf-field-taxonomy.php:648
msgid "Load value from posts terms"
msgstr "从文章项目中加载值"

#: includes/fields/class-acf-field-taxonomy.php:647
msgid "Load Terms"
msgstr "加载项目"

#: includes/fields/class-acf-field-taxonomy.php:637
msgid "Connect selected terms to the post"
msgstr "连接所选项目到文章"

#: includes/fields/class-acf-field-taxonomy.php:636
msgid "Save Terms"
msgstr "保存项目"

#: includes/fields/class-acf-field-taxonomy.php:626
msgid "Allow new terms to be created whilst editing"
msgstr "在编辑时允许可以创建新的项目"

#: includes/fields/class-acf-field-taxonomy.php:625
msgid "Create Terms"
msgstr "创建项目"

#: includes/fields/class-acf-field-taxonomy.php:684
msgid "Radio Buttons"
msgstr "单选框"

#: includes/fields/class-acf-field-taxonomy.php:683
msgid "Single Value"
msgstr "单个值"

#: includes/fields/class-acf-field-taxonomy.php:681
msgid "Multi Select"
msgstr "多选"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:680
msgid "Checkbox"
msgstr "复选框"

#: includes/fields/class-acf-field-taxonomy.php:679
msgid "Multiple Values"
msgstr "多选"

#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Select the appearance of this field"
msgstr "为这个字段选择外观"

#: includes/fields/class-acf-field-taxonomy.php:673
msgid "Appearance"
msgstr "外观"

#: includes/fields/class-acf-field-taxonomy.php:615
msgid "Select the taxonomy to be displayed"
msgstr "选择要显示的分类法"

#: includes/fields/class-acf-field-taxonomy.php:579
msgctxt "No Terms"
msgid "No %s"
msgstr "无 %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "值要小于等于 %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "值要大于等于 %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "值必须是数字"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "数字"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "存档为字段的选择的 'other' 的值"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "为自定义值添加 'other' 选择"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "其他"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "单选按钮"

#: includes/fields/class-acf-field-accordion.php:103
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr "定义上一个手风琴停止的端点。此手风琴将不可见。"

#: includes/fields/class-acf-field-accordion.php:92
msgid "Allow this accordion to open without closing others."
msgstr "允许此手风琴打开而不关闭其他。"

#: includes/fields/class-acf-field-accordion.php:91
msgid "Multi-Expand"
msgstr "多扩展"

#: includes/fields/class-acf-field-accordion.php:81
msgid "Display this accordion as open on page load."
msgstr "将此手风琴显示为在页面加载时打开。"

#: includes/fields/class-acf-field-accordion.php:80
msgid "Open"
msgstr "打开"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "手风琴"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "限制什么类型的文件可以上传"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "文件ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "文件URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "文件数组"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "添加文件"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "没选择文件"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "文件名"

#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:3162 assets/build/js/acf-input.js:3385
msgid "Update File"
msgstr "更新文件"

#: includes/fields/class-acf-field-file.php:56
#: assets/build/js/acf-input.js:3161 assets/build/js/acf-input.js:3384
msgid "Edit File"
msgstr "编辑文件"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
#: assets/build/js/acf-input.js:3135 assets/build/js/acf-input.js:3357
msgid "Select File"
msgstr "选择文件"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "文件"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "密码"

#: includes/fields/class-acf-field-select.php:365
msgid "Specify the value returned"
msgstr "指定返回的值"

#: includes/fields/class-acf-field-select.php:433
msgid "Use AJAX to lazy load choices?"
msgstr "使用 AJAX 惰性选择？"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:354
msgid "Enter each default value on a new line"
msgstr "每行输入一个默认值"

#: includes/fields/class-acf-field-select.php:229 includes/media.php:48
#: assets/build/js/acf-input.js:7821 assets/build/js/acf-input.js:8483
msgctxt "verb"
msgid "Select"
msgstr "选择"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "加载失败"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "搜索中&hellip;"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "载入更多结果&hellip;"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "只能选择 %d 项"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "您只能选择1项"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "请删除 %d 个字符"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "请删除1个字符"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "请输入 %d 或者更多字符"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "请输入至少一个字符"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "找不到匹配项"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d 结果可用, 请使用向上和向下箭头键进行导航。"

#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "一个结果是可用的，按回车选择它。"

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:685
msgctxt "noun"
msgid "Select"
msgstr "下拉选择"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "用户 ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "用户对象"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "數組"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "所有用户角色"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "按角色过滤"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "用户"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "分隔线"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "选择颜色"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "默认"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "清除"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "颜色选择"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "下午"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "上午"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "选择"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "已完成"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "现在"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "时区"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "微秒"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "毫秒"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "秒"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "分钟"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "小时"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "时间"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "选择时间"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "日期时间选择器"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Endpoint"
msgstr "端点"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:109
msgid "Left aligned"
msgstr "左对齐"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:108
msgid "Top aligned"
msgstr "顶部对齐"

#: includes/fields/class-acf-field-tab.php:104
msgid "Placement"
msgstr "位置"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "选项卡"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "值必须是有效的地址"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "链接 URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "链接数组"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "在新窗口/选项卡中打开"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "选择链接"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "链接"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "电子邮件"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "步长"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "最大值"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "最小值"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "范围(滑块)"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:372
msgid "Both (Array)"
msgstr "两个 (阵列)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:371
msgid "Label"
msgstr "标签"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:370
msgid "Value"
msgstr "值"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "垂直"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "水平"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "For more control, you may specify both a value and label like this:"
msgstr "如果需要更多控制，您按照一下格式，定义一个值和标签对："

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "Enter each choice on a new line."
msgstr "输入选项，每行一个。"

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:342
msgid "Choices"
msgstr "选项"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "按钮组"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:401
#: includes/fields/class-acf-field-taxonomy.php:694
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "允许空值"

#: includes/fields/class-acf-field-page_link.php:262
#: includes/fields/class-acf-field-post_object.php:243
#: includes/fields/class-acf-field-taxonomy.php:858
msgid "Parent"
msgstr "父级"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE 在栏位没有点击之前不会初始化"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "延迟初始化"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "显示媒体上传按钮"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "工具条"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "纯文本"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "只有显示"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "显示与文本"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "标签"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "点击初始化 TinyMCE 编辑器"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "文本"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "显示"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "值不得超过%d个字符"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "留空则不限制"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "字符限制"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "在 input 后面显示"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "追加"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "在 input 前面显示"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "前置"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "在 input 内部显示"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "占位符文本"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "创建新文章的时候显示"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "文本"

#: includes/fields/class-acf-field-relationship.php:742
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s 至少需要 %2$s 个选择"

#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:605
msgid "Post ID"
msgstr "文章 ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:390
#: includes/fields/class-acf-field-relationship.php:604
msgid "Post Object"
msgstr "文章对象"

#: includes/fields/class-acf-field-relationship.php:637
msgid "Maximum Posts"
msgstr "最大文章数"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Minimum Posts"
msgstr "最小文章数"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:662
msgid "Featured Image"
msgstr "特色图像"

#: includes/fields/class-acf-field-relationship.php:658
msgid "Selected elements will be displayed in each result"
msgstr "选择的元素将在每个结果中显示"

#: includes/fields/class-acf-field-relationship.php:657
msgid "Elements"
msgstr "元素"

#: includes/fields/class-acf-field-relationship.php:591
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:614
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "分类法"

#: includes/fields/class-acf-field-relationship.php:590
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "文章类型"

#: includes/fields/class-acf-field-relationship.php:584
msgid "Filters"
msgstr "过滤器"

#: includes/fields/class-acf-field-page_link.php:469
#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:577
msgid "All taxonomies"
msgstr "所有分类法"

#: includes/fields/class-acf-field-page_link.php:461
#: includes/fields/class-acf-field-post_object.php:370
#: includes/fields/class-acf-field-relationship.php:569
msgid "Filter by Taxonomy"
msgstr "按分类筛选"

#: includes/fields/class-acf-field-page_link.php:439
#: includes/fields/class-acf-field-post_object.php:348
#: includes/fields/class-acf-field-relationship.php:547
msgid "All post types"
msgstr "所有文章类型"

#: includes/fields/class-acf-field-page_link.php:431
#: includes/fields/class-acf-field-post_object.php:340
#: includes/fields/class-acf-field-relationship.php:539
msgid "Filter by Post Type"
msgstr "按文章类型筛选"

#: includes/fields/class-acf-field-relationship.php:439
msgid "Search..."
msgstr "搜索..."

#: includes/fields/class-acf-field-relationship.php:369
msgid "Select taxonomy"
msgstr "选择分类"

#: includes/fields/class-acf-field-relationship.php:361
msgid "Select post type"
msgstr "选择文章类型"

#: includes/fields/class-acf-field-relationship.php:78
#: assets/build/js/acf-input.js:4937 assets/build/js/acf-input.js:5402
msgid "No matches found"
msgstr "找不到匹配项"

#: includes/fields/class-acf-field-relationship.php:77
#: assets/build/js/acf-input.js:4920 assets/build/js/acf-input.js:5381
msgid "Loading"
msgstr "加载"

#: includes/fields/class-acf-field-relationship.php:76
#: assets/build/js/acf-input.js:4824 assets/build/js/acf-input.js:5271
msgid "Maximum values reached ( {max} values )"
msgstr "达到了最大值 ( {max} 值 )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "关系"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "用英文逗号分隔开，留空则为全部类型"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "允许的文件类型"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "最大"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "文件尺寸"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "限制可以上传的图像"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "最小"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "上传到文章"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "所有"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "限制媒体库的选择"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "库"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "预览图大小"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "图像ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "图像 URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "图像数组"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "指定前端返回的值"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:658
msgid "Return Value"
msgstr "返回值"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "添加图片"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "没有选择图片"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124 assets/build/js/acf.js:1569
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "删除"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "编辑"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
#: assets/build/js/acf-input.js:7868 assets/build/js/acf-input.js:8537
msgid "All images"
msgstr "所有图片"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:4181 assets/build/js/acf-input.js:4579
msgid "Update Image"
msgstr "更新图像"

#: includes/fields/class-acf-field-image.php:61
#: assets/build/js/acf-input.js:4180 assets/build/js/acf-input.js:4578
msgid "Edit Image"
msgstr "编辑图片"

#: includes/fields/class-acf-field-image.php:60
#: assets/build/js/acf-input.js:4016 assets/build/js/acf-input.js:4156
#: assets/build/js/acf-input.js:4404 assets/build/js/acf-input.js:4553
msgid "Select Image"
msgstr "选择图像"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "图像"

#: includes/fields/class-acf-field-message.php:110
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "显示 HTML 文本，而不是渲染 HTML"

#: includes/fields/class-acf-field-message.php:109
msgid "Escape HTML"
msgstr "转义 HTML"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "无格式"

#: includes/fields/class-acf-field-message.php:100
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "自动添加 &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "自动添加段落"

#: includes/fields/class-acf-field-message.php:95
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "控制怎么显示新行"

#: includes/fields/class-acf-field-message.php:94
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "新行"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "每周开始于"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "保存值时使用的格式"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "保存格式"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "周"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "上一页"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "下一个"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "今日"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "完成"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "日期选择"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "宽度"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "嵌入尺寸"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "输入 URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed(嵌入)"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "非激活时显示的文字"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "关闭文本"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "激活时显示的文本"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "打开文本"

#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "风格化的用户界面"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:353
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "默认值"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "在复选框旁边显示文本"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:84
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "消息"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: assets/build/js/acf.js:1746 assets/build/js/acf.js:1861
msgid "No"
msgstr "否"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: assets/build/js/acf.js:1745 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "是"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "真 / 假 (开关)"

#: includes/fields/class-acf-field-group.php:412
msgid "Row"
msgstr "行"

#: includes/fields/class-acf-field-group.php:411
msgid "Table"
msgstr "表"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:410
msgid "Block"
msgstr "区块"

#: includes/fields/class-acf-field-group.php:405
msgid "Specify the style used to render the selected fields"
msgstr "指定用于呈现所选字段的样式"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:404
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "样式"

#: includes/fields/class-acf-field-group.php:388
msgid "Sub Fields"
msgstr "子字段"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "分组"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "自定义地图高度"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "高度"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "设置初始缩放级别"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "缩放"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "居中显示初始地图"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "居中"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "搜索地址..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "搜索当前位置"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "清除位置"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:589
msgid "Search"
msgstr "搜索"

#: includes/fields/class-acf-field-google-map.php:57
#: assets/build/js/acf-input.js:3528 assets/build/js/acf-input.js:3786
msgid "Sorry, this browser does not support geolocation"
msgstr "抱歉，浏览器不支持定位"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "谷歌地图"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "通过模板函数返回的格式"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:385
#: includes/fields/class-acf-field-relationship.php:599
#: includes/fields/class-acf-field-select.php:364
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "返回格式"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "自定义："

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "编辑文章的时候显示的格式"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "显示格式"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "时间选择"

#. translators: counts for inactive field groups
#: acf.php:506
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "已停用 <span class=\"count\">(%s)</span>"

#: acf.php:467
msgid "No Fields found in Trash"
msgstr "回收站里没有字段"

#: acf.php:466
msgid "No Fields found"
msgstr "没找到字段"

#: acf.php:465
msgid "Search Fields"
msgstr "搜索字段"

#: acf.php:464
msgid "View Field"
msgstr "视图字段"

#: acf.php:463 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "新字段"

#: acf.php:462
msgid "Edit Field"
msgstr "编辑字段"

#: acf.php:461
msgid "Add New Field"
msgstr "添加新字段"

#: acf.php:459
msgid "Field"
msgstr "字段"

#: acf.php:458 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "字段"

#: acf.php:433
msgid "No Field Groups found in Trash"
msgstr "回收站中没有找到字段组"

#: acf.php:432
msgid "No Field Groups found"
msgstr "没有找到字段组"

#: acf.php:431
msgid "Search Field Groups"
msgstr "搜索字段组"

#: acf.php:430
msgid "View Field Group"
msgstr "查看字段组"

#: acf.php:429
msgid "New Field Group"
msgstr "新建字段组"

#: acf.php:428
msgid "Edit Field Group"
msgstr "编辑字段组"

#: acf.php:427
msgid "Add New Field Group"
msgstr "添加字段组"

#: acf.php:426 acf.php:460
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "新建"

#: acf.php:425
msgid "Field Group"
msgstr "字段组"

#: acf.php:424 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "字段组"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "【高级自定义字段 ACF】使用强大、专业和直观的字段自定义WordPress。"

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields 专业版"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "选项已更新"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "重新检查"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "发布"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"这个选项页上还没有自定义字段群组。<a href=\"%s\">创建自定义字段群组</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>错误</b>，不能连接到更新服务器"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "显示"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "添加行"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "布局"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "布局"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "这个字段需要至少 {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} 可用 (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} 需要 (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "灵活内容字段需要至少一个布局"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "点击下面的 \"%s\" 按钮创建布局"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "添加布局"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "删除布局"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "删除布局"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "复制布局"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "添加新布局"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "添加布局"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "最小"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "最大"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "最小布局"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "最大布局"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "按钮标签"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "添加图片到相册"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "已到最大选择"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "长度"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "标题"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "添加到相册"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "批量动作"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "按上传日期排序"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "按修改日期排序"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "按标题排序"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "颠倒当前排序"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "关闭"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "最小选择"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "最大选择"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "允许的文字类型"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "已到最小行数 ({min} 行)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "已到最大行数 ({max} 行)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "最大行数"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "拖拽排序"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "添加行"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "删除行"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "首页"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "文章页"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "首页"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "文章页"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "还没有选项页面"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "关闭许可证"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "激活许可证"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "许可证号"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "更新信息"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "当前版本"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "最新版本"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "可用更新"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "更新通知"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "在上面输入许可证号解锁更新"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "更新插件"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
