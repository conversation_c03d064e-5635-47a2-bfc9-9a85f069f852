/*! lazysizes - v5.1.0 */
!function(a,b){var c=function(){b(a.lazySizes),a.removeEventListener("lazyunveilread",c,!0)};b=b.bind(null,a,a.document),"object"==typeof module&&module.exports?b(require("lazysizes")):a.lazySizes?c():a.addEventListener("lazyunveilread",c,!0)}(window,function(a,b,c){"use strict";var d,e,f,g;a.addEventListener&&(d=c&&c.cfg,e=d.lazyClass||"lazyload",f=function(){var a,d;if("string"==typeof e&&(e=b.getElementsByClassName(e)),c)for(a=0,d=e.length;a<d;a++)c.loader.unveil(e[a])},addEventListener("beforeprint",f,!1),!("onbeforeprint"in a)&&a.matchMedia&&(g=matchMedia("print"))&&g.addListener&&g.addListener(function(){g.matches&&f()}))});