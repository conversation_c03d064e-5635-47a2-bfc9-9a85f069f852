function checkImageSizes(){var e=document.getElementsByTagName("img");for(i=0;i<e.length;i++)e[i].classList.remove("scaled-image"),checkImageScale(e[i]);return!1}function checkImageScale(e){var t,a;e.src&&("string"==typeof e.src&&-1<e.src.search(/\.svg/)||"string"==typeof e.src&&-1<e.src.search(/data:image/)||e.naturalWidth&&25<e.naturalWidth&&25<e.naturalHeight&&25<e.clientWidth&&25<e.clientHeight&&(a=window.devicePixelRatio||1,t=1.5*e.clientWidth*a<e.naturalWidth,a=1.5*e.clientHeight*a<e.naturalHeight,(t||a)&&(e.classList.add("scaled-image"),e.title="Forced to wrong size: "+e.clientWidth+"x"+e.clientHeight+", natural is "+e.naturalWidth+"x"+e.naturalHeight+"!")))}function clearScaledImages(){for(var e=document.querySelectorAll("img.scaled-image"),t=0,a=e.length;t<a;t++)e[t].classList.remove("scaled-image")}window.onload=function(){checkImageSizes();var e=document.getElementById("wp-admin-bar-resize-detection");e&&(e.onclick=function(){e.classList.toggle("ewww-fade"),clearScaledImages(),checkImageSizes(),setTimeout(function(){e.classList.toggle("ewww-fade")},500)})},document.addEventListener("lazyloaded",function(e){e.target.classList.remove("scaled-image"),0===e.target.title.search("Forced to wrong size")&&(e.target.title=""),checkImageScale(e.target)});