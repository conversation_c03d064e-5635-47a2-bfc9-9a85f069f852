!function(){function e(e,t,r,n,o,i,a){try{var u=e[i](a),s=u.value}catch(e){r(e);return}u.done?t(s):Promise.resolve(s).then(n,o)}function t(t){return function(){var r=this,n=arguments;return new Promise(function(o,i){var a=t.apply(r,n);function u(t){e(a,o,i,u,s,"next",t)}function s(t){e(a,o,i,u,s,"throw",t)}u(void 0)})}}function r(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function n(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var a,u,s={};s=function(e,t,n){var o,i;if(!e)return n;if(Array.isArray(t)&&(o=t.slice(0)),"string"==typeof t&&(o=t.split(".")),(void 0===t?"undefined":r(t))=="symbol"&&(o=[t]),!Array.isArray(o))throw Error("props arg must be an array, a string or a symbol");for(;o.length;)if(i=o.shift(),!e||void 0===(e=e[i]))return n;return e};var l=function(e,t){if("object"==typeof e&&null!==e){if("object"==typeof e.names&&"object"==typeof t)for(var r=0;r<t.length;r++){var n=t[r];if(e.names[n])return e.names[n]}return e.name?e.name:""}return e},c=function(){var e,t;function n(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),i(this,"data",{}),i(this,"default_locales",[]),this.data=e||{is_empty:!0},this.default_locales=["en"],this.default_locales=this._process_locales(t)}return e=[{key:"get",value:function(e,t){return this.get_with_locales(e,null,t)}},{key:"get_raw",value:function(e){var t;return e=e.split(".").map(function(e){return"string"!=typeof e||"string"!=typeof e[0]?"":e=(e=e[0].toLowerCase()+e.slice(1)).replace(/([A-Z])/g,"_$1").toLowerCase()}).join("."),((t=s)&&t.__esModule?t.default:t)(this.data,e,null)}},{key:"has_property",value:function(e){return null!==this._lookup_with_locales(e,this.default_locales,null)}},{key:"_lookup_with_locales",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";t=this._process_locales(t),".name"===e.substr(-5)&&(e=e.substr(0,e.length-5));var n=this.get_raw(e);return(null===(n=l(n,t))||""===n)&&(n=r),n}},{key:"_process_locales",value:function(e){return"string"==typeof e&&(e=[e]),Array.isArray(e)&&0!==e.length||(e=this.default_locales),e}},{key:"get_with_locales",value:function(e,t,n){var o=this._lookup_with_locales(e,t,n);return("object"==typeof o&&console.warn('Geolocation IP Detection: The property "'+e+'" is of type "'+(void 0===o?"undefined":r(o))+'", should be string or similar',o),void 0===o)?(console.warn('Geolocation IP Detection: The property "'+e+'" is not defined, please check spelling or maybe you need a different data source',{data:this.data}),""):o}},{key:"get_country_iso",value:function(){var e=this.get("country.iso_code");return e&&(e=e.substr(0,2).toLowerCase()),e}},{key:"is_empty",value:function(){return this.get("is_empty",!1)}},{key:"error",value:function(){return this.get_raw("extra.error")||""}},{key:"serialize",value:function(){return this.data}}],o(n.prototype,e),t&&o(n,t),n}(),f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",r=new XMLHttpRequest;return new Promise(function(n,o){r.onreadystatechange=function(){4===r.readyState&&(r.status>=200&&r.status<300?n(r):o({status:r.status,statusText:r.statusText,request:r}))},r.open(t||"GET",e,!0),r.send()})},p=function(e){try{return JSON.parse(e)}catch(t){return d("Invalid JSON: "+e)}};function d(e){return{is_empty:!0,extra:{error:e}}}var h=(a=t(function(e){var t,r,o=arguments;return n(this,function(n){switch(n.label){case 0:t=o.length>1&&void 0!==o[1]?o[1]:"GET",n.label=1;case 1:return n.trys.push([1,3,,4]),[4,f(e,t)];case 2:if(!(r=n.sent()).responseText||"0"===r.responseText)return[2,d("Got an empty response from server. Did you enable AJAX in the options?")];return[2,p(r.responseText)];case 3:return[2,p(n.sent().request.responseText)];case 4:return[2]}})}),function(e){return a.apply(this,arguments)}),y=(null===(u=window.geoip_detect)||void 0===u?void 0:u.options)||{ajaxurl:"/wp-admin/admin-ajax.php",default_locales:["en"],cookie_duration_in_days:7,cookie_name:"geoip-detect-result",do_body_classes:!1},v=function(e,t,r){var n={value:t,expires_at:new Date().getTime()+1e3*r/1};localStorage.setItem(e.toString(),JSON.stringify(n))},_=function(e){var t=null;try{t=JSON.parse(localStorage.getItem(e.toString()))}catch(e){return null}if(null!==t){if(!(null!==t.expires_at&&t.expires_at<new Date().getTime()))return t.value;localStorage.removeItem(e.toString())}return null},g=function(){return _(y.cookie_name)},b=function(e,t){v(y.cookie_name,e,t)},m=null;function w(){return(w=t(function(){var e,t,r,o,i,a;return n(this,function(n){switch(n.label){case 0:if(e=!1,t=!1,y.cookie_name&&(null==(t=g())?void 0:t.extra))return!0===t.extra.override?console.info("Geolocation IP Detection: Using cached response (override)"):console.info("Geolocation IP Detection: Using cached response"),[2,t];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,(m||(m=h(y.ajaxurl+"?action=geoip_detect2_get_info_from_current_ip")).then(function(e){var t;(null==e?void 0:null===(t=e.extra)||void 0===t?void 0:t.error)&&console.error("Geolocation IP Detection Error: Server returned an error: "+e.extra.error)}),m)];case 2:return e=n.sent(),[3,4];case 3:return console.log("Weird: Uncaught error...",r=n.sent()),e=r.responseJSON||r,[3,4];case 4:if(y.cookie_name){if((null==(t=g())?void 0:null===(o=t.extra)||void 0===o?void 0:o.override)===!0)return console.info("Geolocation IP Detection: Using cached response (override)"),[2,t];a=86400*y.cookie_duration_in_days,(null==e?void 0:null===(i=e.extra)||void 0===i?void 0:i.error)&&(a=60),b(e,a)}return[2,e]}})})).apply(this,arguments)}function k(){return(k=t(function(){var e;return n(this,function(t){switch(t.label){case 0:return[4,function(){return w.apply(this,arguments)}()];case 1:return"object"!=typeof(e=t.sent())&&(console.error("Geolocation IP Detection Error: Record should be an object, not a "+(void 0===e?"undefined":r(e)),e),e={extra:{error:e||"Network error, look at the original server response ..."}}),[2,new c(e,y.default_locales)]}})})).apply(this,arguments)}window.geoip_detect.get_info=function(){return k.apply(this,arguments)}}();
//# sourceMappingURL=frontend_base.js.map
