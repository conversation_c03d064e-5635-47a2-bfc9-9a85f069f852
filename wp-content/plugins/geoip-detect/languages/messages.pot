# Copyright (C) 2016 GeoIP Detection
# This file is distributed under the same license as the  package.
msgid ""
msgstr ""
"Project-Id-Version:  GeoIP Detection 2.5.4\n"
"Report-Msgid-Bugs-To: http://wordpress.org/tag/geoip-detect\n"
"POT-Creation-Date: 2016-01-06 01:34:25+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2016-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: admin-ui.php:7
msgid "GeoIP Detection Lookup"
msgstr ""

#: admin-ui.php:7
msgid "GeoIP Lookup"
msgstr ""

#: admin-ui.php:8 views/lookup.php:19 views/options.php:6
msgid "GeoIP Detection"
msgstr ""

#: admin-ui.php:13 views/lookup.php:45
msgid "Lookup"
msgstr ""

#: admin-ui.php:16
msgid "Options"
msgstr ""

#: admin-ui.php:85
msgid "Updated successfully."
msgstr ""

#: admin-ui.php:87
msgid "Update failed."
msgstr ""

#: check_requirements.php:40
msgid "GeoIP Detection: Minimum requirements not met."
msgstr ""

#: data-sources/auto.php:12
msgid "Automatic download & update of Maxmind GeoIP Lite City"
msgstr ""

#: data-sources/auto.php:13
msgid "%s (updated monthly)"
msgstr ""

#: data-sources/auto.php:14
msgid ""
"(License: Creative Commons Attribution-ShareAlike 3.0 Unported. See <a href="
"\"https://github.com/yellowtree/wp-geoip-detect/wiki/FAQ#the-maxmind-lite-"
"databases-are-licensed-creative-commons-sharealike-attribution-when-do-i-"
"need-to-give-attribution\" target=\"_blank\">Licensing FAQ</a> for more "
"details.)"
msgstr ""

#: data-sources/auto.php:21
msgid "Next update: %s"
msgstr ""

#: data-sources/auto.php:21 data-sources/manual.php:36
msgid "Never"
msgstr ""

#: data-sources/auto.php:27
msgid "Update now"
msgstr ""

#: data-sources/auto.php:88
msgid "Downloaded file could not be opened for reading."
msgstr ""

#: data-sources/auto.php:90
msgid "Database could not be written (%s)."
msgstr ""

#: data-sources/hostinfo.php:96
msgid "HostIP.info Web-API"
msgstr ""

#: data-sources/hostinfo.php:98
msgid ""
"Free (Licence: GPL)<br />(only English names, does only have the following "
"fields: country name, country ID and city name)"
msgstr ""

#: data-sources/hostinfo.php:99
msgid "You can choose a Maxmind database below."
msgstr ""

#: data-sources/manual.php:12
msgid "Manual download & update of a Maxmind City or Country database"
msgstr ""

#: data-sources/manual.php:14
msgid ""
"<a href=\"http://dev.maxmind.com/geoip/geoip2/geolite2/\" target=\"_blank"
"\">Free version</a> - <a href=\"https://www.maxmind.com/en/geoip2-country-"
"database\" target=\"_blank\">Commercial Version</a>"
msgstr ""

#: data-sources/manual.php:21
msgid "No Maxmind database found."
msgstr ""

#: data-sources/manual.php:25
msgid "Database file: %s"
msgstr ""

#: data-sources/manual.php:36
msgid "Last updated: %s"
msgstr ""

#: data-sources/manual.php:37
msgid "Database data from: %s"
msgstr ""

#: data-sources/manual.php:50
msgid "Current value: %s"
msgstr ""

#: data-sources/manual.php:71
msgid "The manual datafile has not been found or is not a mmdb-File. "
msgstr ""

#: data-sources/manual.php:91 data-sources/manual.php:137
msgid "Error while creating reader for \"%s\": %s"
msgstr ""

#: data-sources/manual.php:148 data-sources/manual.php:155
msgid "Maxmind File Database (file does not exist or is not readable)"
msgstr ""

#: data-sources/precision.php:64
msgid "Maxmind Precision Web-API"
msgstr ""

#: data-sources/precision.php:66
msgid ""
"<a href=\"https://www.maxmind.com/en/geoip2-precision-services\">Maxmind "
"Precision Services</a>"
msgstr ""

#: data-sources/precision.php:69
msgid "API Type: %s"
msgstr ""

#: data-sources/precision.php:73
msgid "Remaining Credits: ca. %s"
msgstr ""

#: data-sources/precision.php:77 data-sources/precision.php:124
msgid "Maxmind Precision only works with a given user id and secret."
msgstr ""

#: data-sources/precision.php:87
msgid "User ID:"
msgstr ""

#: data-sources/precision.php:88
msgid "User Secret:"
msgstr ""

#: data-sources/precision.php:89
msgid "API Type:"
msgstr ""

#: init.php:48
msgid "Dismiss notice"
msgstr ""

#: init.php:51
msgid "GeoIP Detection: No database installed"
msgstr ""

#: init.php:52
msgid ""
"The Plugin %s is currently using the Webservice <a href=\"http://hostip.info"
"\" target=\"_blank\">hostip.info</a> as data source. <br />You can click on "
"the button below to download and install Maxmind GeoIPv2 Lite City now."
msgstr ""

#: init.php:53
msgid ""
"This database is licenced <a href=\"http://creativecommons.org/licenses/by-"
"sa/3.0/\">CC BY-SA</a>. See <a href=\"http://dev.maxmind.com/geoip/geoip2/"
"geolite2/#License\">License</a> for details."
msgstr ""

#: init.php:62
msgid "Install now"
msgstr ""

#: init.php:63
msgid "Keep using hostip.info"
msgstr ""

#: views/footer.php:4
msgid ""
"This extension is \"charity-ware\". You can use it for free but if you want "
"to do me a favor, please <a href=\"%s\" target=\"_blank\">donate</a> to <a "
"href=\"%s\" target=\"_blank\">this charity</a>. (See <a href=\"%s\" target="
"\"_blank\">FAQ</a> for more infos.)"
msgstr ""

#: views/footer.php:10
msgid ""
"This product includes GeoLite2 data created by MaxMind, available from <a "
"href=\"http://www.maxmind.com/\">http://www.maxmind.com</a>."
msgstr ""

#: views/lookup.php:23 views/options.php:15
msgid "<b>Selected data source:</b> %s"
msgstr ""

#: views/lookup.php:36
msgid "IP"
msgstr ""

#: views/lookup.php:36
msgid "Enter an IP (v4 or v6)"
msgstr ""

#: views/lookup.php:37
msgid "Use these locales:"
msgstr ""

#: views/lookup.php:39
msgid "Default (Current site language, English otherwise)"
msgstr ""

#: views/lookup.php:40
msgid "English only"
msgstr ""

#: views/lookup.php:41
msgid "French, English otherwise"
msgstr ""

#: views/lookup.php:43
msgid "Skip cache"
msgstr ""

#: views/lookup.php:53
msgid "The function %s returns an object:"
msgstr ""

#: views/lookup.php:54
msgid "Lookup duration: %.5f s"
msgstr ""

#: views/lookup.php:56
msgid "(Served from cache. Was cached %s ago)"
msgstr ""

#: views/lookup.php:68
msgid "Key"
msgstr ""

#: views/lookup.php:69
msgid "Value"
msgstr ""

#: views/lookup.php:70
msgid "Shortcode equivalent"
msgstr ""

#: views/lookup.php:123
msgid "No information found about this IP."
msgstr ""

#: views/lookup.php:128
msgid "See %s for more documentation."
msgstr ""

#: views/options.php:7
msgid "Test IP Detection Lookup"
msgstr ""

#: views/options.php:21
msgid "Options for this data source"
msgstr ""

#: views/options.php:28 views/options.php:48 views/options.php:89
msgid "Save"
msgstr ""

#: views/options.php:39
msgid "Choose data source:"
msgstr ""

#: views/options.php:53
msgid "General Options"
msgstr ""

#: views/options.php:55
msgid "Add a country-specific CSS class to the &lt;body&gt;-Tag."
msgstr ""

#: views/options.php:58
msgid ""
"Disable caching a page that contains a shortcode or API call to geo-"
"dependent functions."
msgstr ""

#: views/options.php:60
msgid "At least WP SuperCache, W3TotalCache and ZenCache are supported."
msgstr ""

#: views/options.php:63
msgid ""
"Warning: As the CSS option above is active, this means that all pages are "
"not cached."
msgstr ""

#: views/options.php:68
msgid "The server is behind a reverse proxy"
msgstr ""

#: views/options.php:71
msgid ""
"(With Proxy: %s - Without Proxy: %s - Client IP with current configuration: "
"%s)"
msgstr ""

#: views/options.php:73
msgid "(This doesn't seem to be the case.)"
msgstr ""

#: views/options.php:79
msgid "External IP of this server:"
msgstr ""

#: views/options.php:79
msgid "detect automatically"
msgstr ""

#: views/options.php:81
msgid "Current value:"
msgstr ""

#: views/options.php:82
msgid ""
"If empty: Try to use an ip service to detect it (Internet connection is "
"necessary). If this is not possible, 0.0.0.0 will be returned."
msgstr ""

#: views/options.php:83
msgid ""
"(This external adress will be used when the request IP adress is not a "
"public IP, e.g. 127.0.0.1)"
msgstr ""

#: views/options.php:94
msgid "IPv6 not supported"
msgstr ""

#: views/options.php:96
msgid ""
"Your version of PHP is compiled without IPv6-support, so it is not possible "
"to lookup adresses like \"2001:4860:4801:5::91\". For more information see "
"<a href=\"https://php.net/manual/en/function.inet-pton.php\">PHP "
"documentation & user comments</a>."
msgstr ""
