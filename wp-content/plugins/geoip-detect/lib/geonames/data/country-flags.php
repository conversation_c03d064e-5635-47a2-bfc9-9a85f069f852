<?php
// Generated at Wed, 18 Sep 2019 12:13:06 +0200 
return array (
  'AD' => 
  array (
    'emoji' => '🇦🇩',
    'tel' => '+376',
  ),
  'AE' => 
  array (
    'emoji' => '🇦🇪',
    'tel' => '+971',
  ),
  'AF' => 
  array (
    'emoji' => '🇦🇫',
    'tel' => '+93',
  ),
  'AG' => 
  array (
    'emoji' => '🇦🇬',
    'tel' => '+1268',
  ),
  'AI' => 
  array (
    'emoji' => '🇦🇮',
    'tel' => '******',
  ),
  'AL' => 
  array (
    'emoji' => '🇦🇱',
    'tel' => '+355',
  ),
  'AM' => 
  array (
    'emoji' => '🇦🇲',
    'tel' => '+374',
  ),
  'AO' => 
  array (
    'emoji' => '🇦🇴',
    'tel' => '+244',
  ),
  'AQ' => 
  array (
    'emoji' => '🇦🇶',
    'tel' => '',
  ),
  'AR' => 
  array (
    'emoji' => '🇦🇷',
    'tel' => '+54',
  ),
  'AS' => 
  array (
    'emoji' => '🇦🇸',
    'tel' => '******',
  ),
  'AT' => 
  array (
    'emoji' => '🇦🇹',
    'tel' => '+43',
  ),
  'AU' => 
  array (
    'emoji' => '🇦🇺',
    'tel' => '+61',
  ),
  'AW' => 
  array (
    'emoji' => '🇦🇼',
    'tel' => '+297',
  ),
  'AX' => 
  array (
    'emoji' => '🇦🇽',
    'tel' => '',
  ),
  'AZ' => 
  array (
    'emoji' => '🇦🇿',
    'tel' => '+994',
  ),
  'BA' => 
  array (
    'emoji' => '🇧🇦',
    'tel' => '+387',
  ),
  'BB' => 
  array (
    'emoji' => '🇧🇧',
    'tel' => '******',
  ),
  'BD' => 
  array (
    'emoji' => '🇧🇩',
    'tel' => '+880',
  ),
  'BE' => 
  array (
    'emoji' => '🇧🇪',
    'tel' => '+32',
  ),
  'BF' => 
  array (
    'emoji' => '🇧🇫',
    'tel' => '+226',
  ),
  'BG' => 
  array (
    'emoji' => '🇧🇬',
    'tel' => '+359',
  ),
  'BH' => 
  array (
    'emoji' => '🇧🇭',
    'tel' => '+973',
  ),
  'BI' => 
  array (
    'emoji' => '🇧🇮',
    'tel' => '+257',
  ),
  'BJ' => 
  array (
    'emoji' => '🇧🇯',
    'tel' => '+229',
  ),
  'BL' => 
  array (
    'emoji' => '🇧🇱',
    'tel' => '+590',
  ),
  'BM' => 
  array (
    'emoji' => '🇧🇲',
    'tel' => '******',
  ),
  'BN' => 
  array (
    'emoji' => '🇧🇳',
    'tel' => '+673',
  ),
  'BO' => 
  array (
    'emoji' => '🇧🇴',
    'tel' => '+591',
  ),
  'BQ' => 
  array (
    'emoji' => '🇧🇶',
    'tel' => '',
  ),
  'BR' => 
  array (
    'emoji' => '🇧🇷',
    'tel' => '+55',
  ),
  'BS' => 
  array (
    'emoji' => '🇧🇸',
    'tel' => '******',
  ),
  'BT' => 
  array (
    'emoji' => '🇧🇹',
    'tel' => '+975',
  ),
  'BV' => 
  array (
    'emoji' => '🇧🇻',
    'tel' => '',
  ),
  'BW' => 
  array (
    'emoji' => '🇧🇼',
    'tel' => '+267',
  ),
  'BY' => 
  array (
    'emoji' => '🇧🇾',
    'tel' => '+375',
  ),
  'BZ' => 
  array (
    'emoji' => '🇧🇿',
    'tel' => '+501',
  ),
  'CA' => 
  array (
    'emoji' => '🇨🇦',
    'tel' => '+1',
  ),
  'CC' => 
  array (
    'emoji' => '🇨🇨',
    'tel' => '+61',
  ),
  'CD' => 
  array (
    'emoji' => '🇨🇩',
    'tel' => '+243',
  ),
  'CF' => 
  array (
    'emoji' => '🇨🇫',
    'tel' => '+236',
  ),
  'CG' => 
  array (
    'emoji' => '🇨🇬',
    'tel' => '+242',
  ),
  'CH' => 
  array (
    'emoji' => '🇨🇭',
    'tel' => '+41',
  ),
  'CI' => 
  array (
    'emoji' => '🇨🇮',
    'tel' => '+225',
  ),
  'CK' => 
  array (
    'emoji' => '🇨🇰',
    'tel' => '+682',
  ),
  'CL' => 
  array (
    'emoji' => '🇨🇱',
    'tel' => '+56',
  ),
  'CM' => 
  array (
    'emoji' => '🇨🇲',
    'tel' => '+237',
  ),
  'CN' => 
  array (
    'emoji' => '🇨🇳',
    'tel' => '+86',
  ),
  'CO' => 
  array (
    'emoji' => '🇨🇴',
    'tel' => '+57',
  ),
  'CR' => 
  array (
    'emoji' => '🇨🇷',
    'tel' => '+506',
  ),
  'CU' => 
  array (
    'emoji' => '🇨🇺',
    'tel' => '+53',
  ),
  'CV' => 
  array (
    'emoji' => '🇨🇻',
    'tel' => '+238',
  ),
  'CW' => 
  array (
    'emoji' => '🇨🇼',
    'tel' => '',
  ),
  'CX' => 
  array (
    'emoji' => '🇨🇽',
    'tel' => '+61',
  ),
  'CY' => 
  array (
    'emoji' => '🇨🇾',
    'tel' => '+537',
  ),
  'CZ' => 
  array (
    'emoji' => '🇨🇿',
    'tel' => '+420',
  ),
  'DE' => 
  array (
    'emoji' => '🇩🇪',
    'tel' => '+49',
  ),
  'DJ' => 
  array (
    'emoji' => '🇩🇯',
    'tel' => '+253',
  ),
  'DK' => 
  array (
    'emoji' => '🇩🇰',
    'tel' => '+45',
  ),
  'DM' => 
  array (
    'emoji' => '🇩🇲',
    'tel' => '******',
  ),
  'DO' => 
  array (
    'emoji' => '🇩🇴',
    'tel' => '******',
  ),
  'DZ' => 
  array (
    'emoji' => '🇩🇿',
    'tel' => '+213',
  ),
  'EC' => 
  array (
    'emoji' => '🇪🇨',
    'tel' => '+593',
  ),
  'EE' => 
  array (
    'emoji' => '🇪🇪',
    'tel' => '+372',
  ),
  'EG' => 
  array (
    'emoji' => '🇪🇬',
    'tel' => '+20',
  ),
  'EH' => 
  array (
    'emoji' => '🇪🇭',
    'tel' => '',
  ),
  'ER' => 
  array (
    'emoji' => '🇪🇷',
    'tel' => '+291',
  ),
  'ES' => 
  array (
    'emoji' => '🇪🇸',
    'tel' => '+34',
  ),
  'ET' => 
  array (
    'emoji' => '🇪🇹',
    'tel' => '+251',
  ),
  'EU' => 
  array (
    'emoji' => '🇪🇺',
    'tel' => '',
  ),
  'FI' => 
  array (
    'emoji' => '🇫🇮',
    'tel' => '+358',
  ),
  'FJ' => 
  array (
    'emoji' => '🇫🇯',
    'tel' => '+679',
  ),
  'FK' => 
  array (
    'emoji' => '🇫🇰',
    'tel' => '+500',
  ),
  'FM' => 
  array (
    'emoji' => '🇫🇲',
    'tel' => '+691',
  ),
  'FO' => 
  array (
    'emoji' => '🇫🇴',
    'tel' => '+298',
  ),
  'FR' => 
  array (
    'emoji' => '🇫🇷',
    'tel' => '+33',
  ),
  'GA' => 
  array (
    'emoji' => '🇬🇦',
    'tel' => '+241',
  ),
  'GB' => 
  array (
    'emoji' => '🇬🇧',
    'tel' => '+44',
  ),
  'GD' => 
  array (
    'emoji' => '🇬🇩',
    'tel' => '******',
  ),
  'GE' => 
  array (
    'emoji' => '🇬🇪',
    'tel' => '+995',
  ),
  'GF' => 
  array (
    'emoji' => '🇬🇫',
    'tel' => '+594',
  ),
  'GG' => 
  array (
    'emoji' => '🇬🇬',
    'tel' => '+44',
  ),
  'GH' => 
  array (
    'emoji' => '🇬🇭',
    'tel' => '+233',
  ),
  'GI' => 
  array (
    'emoji' => '🇬🇮',
    'tel' => '+350',
  ),
  'GL' => 
  array (
    'emoji' => '🇬🇱',
    'tel' => '+299',
  ),
  'GM' => 
  array (
    'emoji' => '🇬🇲',
    'tel' => '+220',
  ),
  'GN' => 
  array (
    'emoji' => '🇬🇳',
    'tel' => '+224',
  ),
  'GP' => 
  array (
    'emoji' => '🇬🇵',
    'tel' => '+590',
  ),
  'GQ' => 
  array (
    'emoji' => '🇬🇶',
    'tel' => '+240',
  ),
  'GR' => 
  array (
    'emoji' => '🇬🇷',
    'tel' => '+30',
  ),
  'GS' => 
  array (
    'emoji' => '🇬🇸',
    'tel' => '+500',
  ),
  'GT' => 
  array (
    'emoji' => '🇬🇹',
    'tel' => '+502',
  ),
  'GU' => 
  array (
    'emoji' => '🇬🇺',
    'tel' => '******',
  ),
  'GW' => 
  array (
    'emoji' => '🇬🇼',
    'tel' => '+245',
  ),
  'GY' => 
  array (
    'emoji' => '🇬🇾',
    'tel' => '+595',
  ),
  'HK' => 
  array (
    'emoji' => '🇭🇰',
    'tel' => '+852',
  ),
  'HM' => 
  array (
    'emoji' => '🇭🇲',
    'tel' => '',
  ),
  'HN' => 
  array (
    'emoji' => '🇭🇳',
    'tel' => '+504',
  ),
  'HR' => 
  array (
    'emoji' => '🇭🇷',
    'tel' => '+385',
  ),
  'HT' => 
  array (
    'emoji' => '🇭🇹',
    'tel' => '+509',
  ),
  'HU' => 
  array (
    'emoji' => '🇭🇺',
    'tel' => '+36',
  ),
  'ID' => 
  array (
    'emoji' => '🇮🇩',
    'tel' => '+62',
  ),
  'IE' => 
  array (
    'emoji' => '🇮🇪',
    'tel' => '+353',
  ),
  'IL' => 
  array (
    'emoji' => '🇮🇱',
    'tel' => '+972',
  ),
  'IM' => 
  array (
    'emoji' => '🇮🇲',
    'tel' => '+44',
  ),
  'IN' => 
  array (
    'emoji' => '🇮🇳',
    'tel' => '+91',
  ),
  'IO' => 
  array (
    'emoji' => '🇮🇴',
    'tel' => '+246',
  ),
  'IQ' => 
  array (
    'emoji' => '🇮🇶',
    'tel' => '+964',
  ),
  'IR' => 
  array (
    'emoji' => '🇮🇷',
    'tel' => '+98',
  ),
  'IS' => 
  array (
    'emoji' => '🇮🇸',
    'tel' => '+354',
  ),
  'IT' => 
  array (
    'emoji' => '🇮🇹',
    'tel' => '+39',
  ),
  'JE' => 
  array (
    'emoji' => '🇯🇪',
    'tel' => '+44',
  ),
  'JM' => 
  array (
    'emoji' => '🇯🇲',
    'tel' => '******',
  ),
  'JO' => 
  array (
    'emoji' => '🇯🇴',
    'tel' => '+962',
  ),
  'JP' => 
  array (
    'emoji' => '🇯🇵',
    'tel' => '+81',
  ),
  'KE' => 
  array (
    'emoji' => '🇰🇪',
    'tel' => '+254',
  ),
  'KG' => 
  array (
    'emoji' => '🇰🇬',
    'tel' => '+996',
  ),
  'KH' => 
  array (
    'emoji' => '🇰🇭',
    'tel' => '+855',
  ),
  'KI' => 
  array (
    'emoji' => '🇰🇮',
    'tel' => '+686',
  ),
  'KM' => 
  array (
    'emoji' => '🇰🇲',
    'tel' => '+269',
  ),
  'KN' => 
  array (
    'emoji' => '🇰🇳',
    'tel' => '******',
  ),
  'KP' => 
  array (
    'emoji' => '🇰🇵',
    'tel' => '+850',
  ),
  'KR' => 
  array (
    'emoji' => '🇰🇷',
    'tel' => '+82',
  ),
  'KW' => 
  array (
    'emoji' => '🇰🇼',
    'tel' => '+965',
  ),
  'KY' => 
  array (
    'emoji' => '🇰🇾',
    'tel' => '+ 345',
  ),
  'KZ' => 
  array (
    'emoji' => '🇰🇿',
    'tel' => '****',
  ),
  'LA' => 
  array (
    'emoji' => '🇱🇦',
    'tel' => '+856',
  ),
  'LB' => 
  array (
    'emoji' => '🇱🇧',
    'tel' => '+961',
  ),
  'LC' => 
  array (
    'emoji' => '🇱🇨',
    'tel' => '******',
  ),
  'LI' => 
  array (
    'emoji' => '🇱🇮',
    'tel' => '+423',
  ),
  'LK' => 
  array (
    'emoji' => '🇱🇰',
    'tel' => '+94',
  ),
  'LR' => 
  array (
    'emoji' => '🇱🇷',
    'tel' => '+231',
  ),
  'LS' => 
  array (
    'emoji' => '🇱🇸',
    'tel' => '+266',
  ),
  'LT' => 
  array (
    'emoji' => '🇱🇹',
    'tel' => '+370',
  ),
  'LU' => 
  array (
    'emoji' => '🇱🇺',
    'tel' => '+352',
  ),
  'LV' => 
  array (
    'emoji' => '🇱🇻',
    'tel' => '+371',
  ),
  'LY' => 
  array (
    'emoji' => '🇱🇾',
    'tel' => '+218',
  ),
  'MA' => 
  array (
    'emoji' => '🇲🇦',
    'tel' => '+212',
  ),
  'MC' => 
  array (
    'emoji' => '🇲🇨',
    'tel' => '+377',
  ),
  'MD' => 
  array (
    'emoji' => '🇲🇩',
    'tel' => '+373',
  ),
  'ME' => 
  array (
    'emoji' => '🇲🇪',
    'tel' => '+382',
  ),
  'MF' => 
  array (
    'emoji' => '🇲🇫',
    'tel' => '+590',
  ),
  'MG' => 
  array (
    'emoji' => '🇲🇬',
    'tel' => '+261',
  ),
  'MH' => 
  array (
    'emoji' => '🇲🇭',
    'tel' => '+692',
  ),
  'MK' => 
  array (
    'emoji' => '🇲🇰',
    'tel' => '+389',
  ),
  'ML' => 
  array (
    'emoji' => '🇲🇱',
    'tel' => '+223',
  ),
  'MM' => 
  array (
    'emoji' => '🇲🇲',
    'tel' => '+95',
  ),
  'MN' => 
  array (
    'emoji' => '🇲🇳',
    'tel' => '+976',
  ),
  'MO' => 
  array (
    'emoji' => '🇲🇴',
    'tel' => '+853',
  ),
  'MP' => 
  array (
    'emoji' => '🇲🇵',
    'tel' => '******',
  ),
  'MQ' => 
  array (
    'emoji' => '🇲🇶',
    'tel' => '+596',
  ),
  'MR' => 
  array (
    'emoji' => '🇲🇷',
    'tel' => '+222',
  ),
  'MS' => 
  array (
    'emoji' => '🇲🇸',
    'tel' => '+1664',
  ),
  'MT' => 
  array (
    'emoji' => '🇲🇹',
    'tel' => '+356',
  ),
  'MU' => 
  array (
    'emoji' => '🇲🇺',
    'tel' => '+230',
  ),
  'MV' => 
  array (
    'emoji' => '🇲🇻',
    'tel' => '+960',
  ),
  'MW' => 
  array (
    'emoji' => '🇲🇼',
    'tel' => '+265',
  ),
  'MX' => 
  array (
    'emoji' => '🇲🇽',
    'tel' => '+52',
  ),
  'MY' => 
  array (
    'emoji' => '🇲🇾',
    'tel' => '+60',
  ),
  'MZ' => 
  array (
    'emoji' => '🇲🇿',
    'tel' => '+258',
  ),
  'NA' => 
  array (
    'emoji' => '🇳🇦',
    'tel' => '+264',
  ),
  'NC' => 
  array (
    'emoji' => '🇳🇨',
    'tel' => '+687',
  ),
  'NE' => 
  array (
    'emoji' => '🇳🇪',
    'tel' => '+227',
  ),
  'NF' => 
  array (
    'emoji' => '🇳🇫',
    'tel' => '+672',
  ),
  'NG' => 
  array (
    'emoji' => '🇳🇬',
    'tel' => '+234',
  ),
  'NI' => 
  array (
    'emoji' => '🇳🇮',
    'tel' => '+505',
  ),
  'NL' => 
  array (
    'emoji' => '🇳🇱',
    'tel' => '+31',
  ),
  'NO' => 
  array (
    'emoji' => '🇳🇴',
    'tel' => '+47',
  ),
  'NP' => 
  array (
    'emoji' => '🇳🇵',
    'tel' => '+977',
  ),
  'NR' => 
  array (
    'emoji' => '🇳🇷',
    'tel' => '+674',
  ),
  'NU' => 
  array (
    'emoji' => '🇳🇺',
    'tel' => '+683',
  ),
  'NZ' => 
  array (
    'emoji' => '🇳🇿',
    'tel' => '+64',
  ),
  'OM' => 
  array (
    'emoji' => '🇴🇲',
    'tel' => '+968',
  ),
  'PA' => 
  array (
    'emoji' => '🇵🇦',
    'tel' => '+507',
  ),
  'PE' => 
  array (
    'emoji' => '🇵🇪',
    'tel' => '+51',
  ),
  'PF' => 
  array (
    'emoji' => '🇵🇫',
    'tel' => '+689',
  ),
  'PG' => 
  array (
    'emoji' => '🇵🇬',
    'tel' => '+675',
  ),
  'PH' => 
  array (
    'emoji' => '🇵🇭',
    'tel' => '+63',
  ),
  'PK' => 
  array (
    'emoji' => '🇵🇰',
    'tel' => '+92',
  ),
  'PL' => 
  array (
    'emoji' => '🇵🇱',
    'tel' => '+48',
  ),
  'PM' => 
  array (
    'emoji' => '🇵🇲',
    'tel' => '+508',
  ),
  'PN' => 
  array (
    'emoji' => '🇵🇳',
    'tel' => '+872',
  ),
  'PR' => 
  array (
    'emoji' => '🇵🇷',
    'tel' => '******',
  ),
  'PS' => 
  array (
    'emoji' => '🇵🇸',
    'tel' => '+970',
  ),
  'PT' => 
  array (
    'emoji' => '🇵🇹',
    'tel' => '+351',
  ),
  'PW' => 
  array (
    'emoji' => '🇵🇼',
    'tel' => '+680',
  ),
  'PY' => 
  array (
    'emoji' => '🇵🇾',
    'tel' => '+595',
  ),
  'QA' => 
  array (
    'emoji' => '🇶🇦',
    'tel' => '+974',
  ),
  'RE' => 
  array (
    'emoji' => '🇷🇪',
    'tel' => '+262',
  ),
  'RO' => 
  array (
    'emoji' => '🇷🇴',
    'tel' => '+40',
  ),
  'RS' => 
  array (
    'emoji' => '🇷🇸',
    'tel' => '+381',
  ),
  'RU' => 
  array (
    'emoji' => '🇷🇺',
    'tel' => '+7',
  ),
  'RW' => 
  array (
    'emoji' => '🇷🇼',
    'tel' => '+250',
  ),
  'SA' => 
  array (
    'emoji' => '🇸🇦',
    'tel' => '+966',
  ),
  'SB' => 
  array (
    'emoji' => '🇸🇧',
    'tel' => '+677',
  ),
  'SC' => 
  array (
    'emoji' => '🇸🇨',
    'tel' => '+248',
  ),
  'SD' => 
  array (
    'emoji' => '🇸🇩',
    'tel' => '+249',
  ),
  'SE' => 
  array (
    'emoji' => '🇸🇪',
    'tel' => '+46',
  ),
  'SG' => 
  array (
    'emoji' => '🇸🇬',
    'tel' => '+65',
  ),
  'SH' => 
  array (
    'emoji' => '🇸🇭',
    'tel' => '+290',
  ),
  'SI' => 
  array (
    'emoji' => '🇸🇮',
    'tel' => '+386',
  ),
  'SJ' => 
  array (
    'emoji' => '🇸🇯',
    'tel' => '+47',
  ),
  'SK' => 
  array (
    'emoji' => '🇸🇰',
    'tel' => '+421',
  ),
  'SL' => 
  array (
    'emoji' => '🇸🇱',
    'tel' => '+232',
  ),
  'SM' => 
  array (
    'emoji' => '🇸🇲',
    'tel' => '+378',
  ),
  'SN' => 
  array (
    'emoji' => '🇸🇳',
    'tel' => '+221',
  ),
  'SO' => 
  array (
    'emoji' => '🇸🇴',
    'tel' => '+252',
  ),
  'SR' => 
  array (
    'emoji' => '🇸🇷',
    'tel' => '+597',
  ),
  'SS' => 
  array (
    'emoji' => '🇸🇸',
    'tel' => '',
  ),
  'ST' => 
  array (
    'emoji' => '🇸🇹',
    'tel' => '+239',
  ),
  'SV' => 
  array (
    'emoji' => '🇸🇻',
    'tel' => '+503',
  ),
  'SX' => 
  array (
    'emoji' => '🇸🇽',
    'tel' => '',
  ),
  'SY' => 
  array (
    'emoji' => '🇸🇾',
    'tel' => '+963',
  ),
  'SZ' => 
  array (
    'emoji' => '🇸🇿',
    'tel' => '+268',
  ),
  'TC' => 
  array (
    'emoji' => '🇹🇨',
    'tel' => '******',
  ),
  'TD' => 
  array (
    'emoji' => '🇹🇩',
    'tel' => '+235',
  ),
  'TF' => 
  array (
    'emoji' => '🇹🇫',
    'tel' => '',
  ),
  'TG' => 
  array (
    'emoji' => '🇹🇬',
    'tel' => '+228',
  ),
  'TH' => 
  array (
    'emoji' => '🇹🇭',
    'tel' => '+66',
  ),
  'TJ' => 
  array (
    'emoji' => '🇹🇯',
    'tel' => '+992',
  ),
  'TK' => 
  array (
    'emoji' => '🇹🇰',
    'tel' => '+690',
  ),
  'TL' => 
  array (
    'emoji' => '🇹🇱',
    'tel' => '+670',
  ),
  'TM' => 
  array (
    'emoji' => '🇹🇲',
    'tel' => '+993',
  ),
  'TN' => 
  array (
    'emoji' => '🇹🇳',
    'tel' => '+216',
  ),
  'TO' => 
  array (
    'emoji' => '🇹🇴',
    'tel' => '+676',
  ),
  'TR' => 
  array (
    'emoji' => '🇹🇷',
    'tel' => '+90',
  ),
  'TT' => 
  array (
    'emoji' => '🇹🇹',
    'tel' => '******',
  ),
  'TV' => 
  array (
    'emoji' => '🇹🇻',
    'tel' => '+688',
  ),
  'TW' => 
  array (
    'emoji' => '🇹🇼',
    'tel' => '+886',
  ),
  'TZ' => 
  array (
    'emoji' => '🇹🇿',
    'tel' => '+255',
  ),
  'UA' => 
  array (
    'emoji' => '🇺🇦',
    'tel' => '+380',
  ),
  'UG' => 
  array (
    'emoji' => '🇺🇬',
    'tel' => '+256',
  ),
  'UM' => 
  array (
    'emoji' => '🇺🇲',
    'tel' => '',
  ),
  'US' => 
  array (
    'emoji' => '🇺🇸',
    'tel' => '+1',
  ),
  'UY' => 
  array (
    'emoji' => '🇺🇾',
    'tel' => '+598',
  ),
  'UZ' => 
  array (
    'emoji' => '🇺🇿',
    'tel' => '+998',
  ),
  'VA' => 
  array (
    'emoji' => '🇻🇦',
    'tel' => '+379',
  ),
  'VC' => 
  array (
    'emoji' => '🇻🇨',
    'tel' => '******',
  ),
  'VE' => 
  array (
    'emoji' => '🇻🇪',
    'tel' => '+58',
  ),
  'VG' => 
  array (
    'emoji' => '🇻🇬',
    'tel' => '******',
  ),
  'VI' => 
  array (
    'emoji' => '🇻🇮',
    'tel' => '******',
  ),
  'VN' => 
  array (
    'emoji' => '🇻🇳',
    'tel' => '+84',
  ),
  'VU' => 
  array (
    'emoji' => '🇻🇺',
    'tel' => '+678',
  ),
  'WF' => 
  array (
    'emoji' => '🇼🇫',
    'tel' => '+681',
  ),
  'WS' => 
  array (
    'emoji' => '🇼🇸',
    'tel' => '+685',
  ),
  'XK' => 
  array (
    'emoji' => '🇽🇰',
    'tel' => '+383',
  ),
  'YE' => 
  array (
    'emoji' => '🇾🇪',
    'tel' => '+967',
  ),
  'YT' => 
  array (
    'emoji' => '🇾🇹',
    'tel' => '+262',
  ),
  'ZA' => 
  array (
    'emoji' => '🇿🇦',
    'tel' => '+27',
  ),
  'ZM' => 
  array (
    'emoji' => '🇿🇲',
    'tel' => '+260',
  ),
  'ZW' => 
  array (
    'emoji' => '🇿🇼',
    'tel' => '+263',
  ),
);