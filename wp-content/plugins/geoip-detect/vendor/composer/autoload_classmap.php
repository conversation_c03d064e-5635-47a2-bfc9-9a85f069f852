<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Composer\\CaBundle\\CaBundle' => $vendorDir . '/composer/ca-bundle/src/CaBundle.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'GeoIp2\\Compat\\JsonSerializable' => $vendorDir . '/geoip2/geoip2/src/Compat/JsonSerializable.php',
    'GeoIp2\\Database\\Reader' => $vendorDir . '/geoip2/geoip2/src/Database/Reader.php',
    'GeoIp2\\Exception\\AddressNotFoundException' => $vendorDir . '/geoip2/geoip2/src/Exception/AddressNotFoundException.php',
    'GeoIp2\\Exception\\AuthenticationException' => $vendorDir . '/geoip2/geoip2/src/Exception/AuthenticationException.php',
    'GeoIp2\\Exception\\GeoIp2Exception' => $vendorDir . '/geoip2/geoip2/src/Exception/GeoIp2Exception.php',
    'GeoIp2\\Exception\\HttpException' => $vendorDir . '/geoip2/geoip2/src/Exception/HttpException.php',
    'GeoIp2\\Exception\\InvalidRequestException' => $vendorDir . '/geoip2/geoip2/src/Exception/InvalidRequestException.php',
    'GeoIp2\\Exception\\OutOfQueriesException' => $vendorDir . '/geoip2/geoip2/src/Exception/OutOfQueriesException.php',
    'GeoIp2\\Model\\AbstractModel' => $vendorDir . '/geoip2/geoip2/src/Model/AbstractModel.php',
    'GeoIp2\\Model\\AnonymousIp' => $vendorDir . '/geoip2/geoip2/src/Model/AnonymousIp.php',
    'GeoIp2\\Model\\Asn' => $vendorDir . '/geoip2/geoip2/src/Model/Asn.php',
    'GeoIp2\\Model\\City' => $vendorDir . '/geoip2/geoip2/src/Model/City.php',
    'GeoIp2\\Model\\ConnectionType' => $vendorDir . '/geoip2/geoip2/src/Model/ConnectionType.php',
    'GeoIp2\\Model\\Country' => $vendorDir . '/geoip2/geoip2/src/Model/Country.php',
    'GeoIp2\\Model\\Domain' => $vendorDir . '/geoip2/geoip2/src/Model/Domain.php',
    'GeoIp2\\Model\\Enterprise' => $vendorDir . '/geoip2/geoip2/src/Model/Enterprise.php',
    'GeoIp2\\Model\\Insights' => $vendorDir . '/geoip2/geoip2/src/Model/Insights.php',
    'GeoIp2\\Model\\Isp' => $vendorDir . '/geoip2/geoip2/src/Model/Isp.php',
    'GeoIp2\\ProviderInterface' => $vendorDir . '/geoip2/geoip2/src/ProviderInterface.php',
    'GeoIp2\\Record\\AbstractPlaceRecord' => $vendorDir . '/geoip2/geoip2/src/Record/AbstractPlaceRecord.php',
    'GeoIp2\\Record\\AbstractRecord' => $vendorDir . '/geoip2/geoip2/src/Record/AbstractRecord.php',
    'GeoIp2\\Record\\City' => $vendorDir . '/geoip2/geoip2/src/Record/City.php',
    'GeoIp2\\Record\\Continent' => $vendorDir . '/geoip2/geoip2/src/Record/Continent.php',
    'GeoIp2\\Record\\Country' => $vendorDir . '/geoip2/geoip2/src/Record/Country.php',
    'GeoIp2\\Record\\Location' => $vendorDir . '/geoip2/geoip2/src/Record/Location.php',
    'GeoIp2\\Record\\MaxMind' => $vendorDir . '/geoip2/geoip2/src/Record/MaxMind.php',
    'GeoIp2\\Record\\Postal' => $vendorDir . '/geoip2/geoip2/src/Record/Postal.php',
    'GeoIp2\\Record\\RepresentedCountry' => $vendorDir . '/geoip2/geoip2/src/Record/RepresentedCountry.php',
    'GeoIp2\\Record\\Subdivision' => $vendorDir . '/geoip2/geoip2/src/Record/Subdivision.php',
    'GeoIp2\\Record\\Traits' => $vendorDir . '/geoip2/geoip2/src/Record/Traits.php',
    'GeoIp2\\Util' => $vendorDir . '/geoip2/geoip2/src/Util.php',
    'GeoIp2\\WebService\\Client' => $vendorDir . '/geoip2/geoip2/src/WebService/Client.php',
    'MaxMind\\Db\\Reader' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader.php',
    'MaxMind\\Db\\Reader\\Decoder' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Decoder.php',
    'MaxMind\\Db\\Reader\\InvalidDatabaseException' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/InvalidDatabaseException.php',
    'MaxMind\\Db\\Reader\\Metadata' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Metadata.php',
    'MaxMind\\Db\\Reader\\Util' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Util.php',
    'MaxMind\\Exception\\AuthenticationException' => $vendorDir . '/maxmind/web-service-common/src/Exception/AuthenticationException.php',
    'MaxMind\\Exception\\HttpException' => $vendorDir . '/maxmind/web-service-common/src/Exception/HttpException.php',
    'MaxMind\\Exception\\InsufficientFundsException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InsufficientFundsException.php',
    'MaxMind\\Exception\\InvalidInputException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InvalidInputException.php',
    'MaxMind\\Exception\\InvalidRequestException' => $vendorDir . '/maxmind/web-service-common/src/Exception/InvalidRequestException.php',
    'MaxMind\\Exception\\IpAddressNotFoundException' => $vendorDir . '/maxmind/web-service-common/src/Exception/IpAddressNotFoundException.php',
    'MaxMind\\Exception\\PermissionRequiredException' => $vendorDir . '/maxmind/web-service-common/src/Exception/PermissionRequiredException.php',
    'MaxMind\\Exception\\WebServiceException' => $vendorDir . '/maxmind/web-service-common/src/Exception/WebServiceException.php',
    'MaxMind\\WebService\\Client' => $vendorDir . '/maxmind/web-service-common/src/WebService/Client.php',
    'MaxMind\\WebService\\Http\\CurlRequest' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/CurlRequest.php',
    'MaxMind\\WebService\\Http\\Request' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/Request.php',
    'MaxMind\\WebService\\Http\\RequestFactory' => $vendorDir . '/maxmind/web-service-common/src/WebService/Http/RequestFactory.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\HttpFoundation\\IpUtils' => $vendorDir . '/symfony/http-foundation/IpUtils.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\AccessException' => $vendorDir . '/symfony/property-access/Exception/AccessException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/property-access/Exception/ExceptionInterface.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/property-access/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\InvalidPropertyPathException' => $vendorDir . '/symfony/property-access/Exception/InvalidPropertyPathException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\NoSuchIndexException' => $vendorDir . '/symfony/property-access/Exception/NoSuchIndexException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\NoSuchPropertyException' => $vendorDir . '/symfony/property-access/Exception/NoSuchPropertyException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\OutOfBoundsException' => $vendorDir . '/symfony/property-access/Exception/OutOfBoundsException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\RuntimeException' => $vendorDir . '/symfony/property-access/Exception/RuntimeException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\UnexpectedTypeException' => $vendorDir . '/symfony/property-access/Exception/UnexpectedTypeException.php',
    'Symfony\\Component\\PropertyAccess\\Exception\\UninitializedPropertyException' => $vendorDir . '/symfony/property-access/Exception/UninitializedPropertyException.php',
    'Symfony\\Component\\PropertyAccess\\PropertyAccess' => $vendorDir . '/symfony/property-access/PropertyAccess.php',
    'Symfony\\Component\\PropertyAccess\\PropertyAccessor' => $vendorDir . '/symfony/property-access/PropertyAccessor.php',
    'Symfony\\Component\\PropertyAccess\\PropertyAccessorBuilder' => $vendorDir . '/symfony/property-access/PropertyAccessorBuilder.php',
    'Symfony\\Component\\PropertyAccess\\PropertyAccessorInterface' => $vendorDir . '/symfony/property-access/PropertyAccessorInterface.php',
    'Symfony\\Component\\PropertyAccess\\PropertyPath' => $vendorDir . '/symfony/property-access/PropertyPath.php',
    'Symfony\\Component\\PropertyAccess\\PropertyPathBuilder' => $vendorDir . '/symfony/property-access/PropertyPathBuilder.php',
    'Symfony\\Component\\PropertyAccess\\PropertyPathInterface' => $vendorDir . '/symfony/property-access/PropertyPathInterface.php',
    'Symfony\\Component\\PropertyAccess\\PropertyPathIterator' => $vendorDir . '/symfony/property-access/PropertyPathIterator.php',
    'Symfony\\Component\\PropertyAccess\\PropertyPathIteratorInterface' => $vendorDir . '/symfony/property-access/PropertyPathIteratorInterface.php',
    'Symfony\\Component\\PropertyAccess\\StringUtil' => $vendorDir . '/symfony/property-access/StringUtil.php',
    'Symfony\\Component\\PropertyInfo\\DependencyInjection\\PropertyInfoConstructorPass' => $vendorDir . '/symfony/property-info/DependencyInjection/PropertyInfoConstructorPass.php',
    'Symfony\\Component\\PropertyInfo\\DependencyInjection\\PropertyInfoPass' => $vendorDir . '/symfony/property-info/DependencyInjection/PropertyInfoPass.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\ConstructorArgumentTypeExtractorInterface' => $vendorDir . '/symfony/property-info/Extractor/ConstructorArgumentTypeExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\ConstructorExtractor' => $vendorDir . '/symfony/property-info/Extractor/ConstructorExtractor.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\PhpDocExtractor' => $vendorDir . '/symfony/property-info/Extractor/PhpDocExtractor.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\PhpStanExtractor' => $vendorDir . '/symfony/property-info/Extractor/PhpStanExtractor.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\ReflectionExtractor' => $vendorDir . '/symfony/property-info/Extractor/ReflectionExtractor.php',
    'Symfony\\Component\\PropertyInfo\\Extractor\\SerializerExtractor' => $vendorDir . '/symfony/property-info/Extractor/SerializerExtractor.php',
    'Symfony\\Component\\PropertyInfo\\PhpStan\\NameScope' => $vendorDir . '/symfony/property-info/PhpStan/NameScope.php',
    'Symfony\\Component\\PropertyInfo\\PhpStan\\NameScopeFactory' => $vendorDir . '/symfony/property-info/PhpStan/NameScopeFactory.php',
    'Symfony\\Component\\PropertyInfo\\PropertyAccessExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyAccessExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyDescriptionExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyDescriptionExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyInfoCacheExtractor' => $vendorDir . '/symfony/property-info/PropertyInfoCacheExtractor.php',
    'Symfony\\Component\\PropertyInfo\\PropertyInfoExtractor' => $vendorDir . '/symfony/property-info/PropertyInfoExtractor.php',
    'Symfony\\Component\\PropertyInfo\\PropertyInfoExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyInfoExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyInitializableExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyInitializableExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyListExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyListExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyReadInfo' => $vendorDir . '/symfony/property-info/PropertyReadInfo.php',
    'Symfony\\Component\\PropertyInfo\\PropertyReadInfoExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyReadInfoExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyTypeExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyTypeExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\PropertyWriteInfo' => $vendorDir . '/symfony/property-info/PropertyWriteInfo.php',
    'Symfony\\Component\\PropertyInfo\\PropertyWriteInfoExtractorInterface' => $vendorDir . '/symfony/property-info/PropertyWriteInfoExtractorInterface.php',
    'Symfony\\Component\\PropertyInfo\\Type' => $vendorDir . '/symfony/property-info/Type.php',
    'Symfony\\Component\\PropertyInfo\\Util\\PhpDocTypeHelper' => $vendorDir . '/symfony/property-info/Util/PhpDocTypeHelper.php',
    'Symfony\\Component\\PropertyInfo\\Util\\PhpStanTypeHelper' => $vendorDir . '/symfony/property-info/Util/PhpStanTypeHelper.php',
    'Symfony\\Component\\String\\AbstractString' => $vendorDir . '/symfony/string/AbstractString.php',
    'Symfony\\Component\\String\\AbstractUnicodeString' => $vendorDir . '/symfony/string/AbstractUnicodeString.php',
    'Symfony\\Component\\String\\ByteString' => $vendorDir . '/symfony/string/ByteString.php',
    'Symfony\\Component\\String\\CodePointString' => $vendorDir . '/symfony/string/CodePointString.php',
    'Symfony\\Component\\String\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/string/Exception/ExceptionInterface.php',
    'Symfony\\Component\\String\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/string/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\String\\Exception\\RuntimeException' => $vendorDir . '/symfony/string/Exception/RuntimeException.php',
    'Symfony\\Component\\String\\Inflector\\EnglishInflector' => $vendorDir . '/symfony/string/Inflector/EnglishInflector.php',
    'Symfony\\Component\\String\\Inflector\\FrenchInflector' => $vendorDir . '/symfony/string/Inflector/FrenchInflector.php',
    'Symfony\\Component\\String\\Inflector\\InflectorInterface' => $vendorDir . '/symfony/string/Inflector/InflectorInterface.php',
    'Symfony\\Component\\String\\LazyString' => $vendorDir . '/symfony/string/LazyString.php',
    'Symfony\\Component\\String\\Slugger\\AsciiSlugger' => $vendorDir . '/symfony/string/Slugger/AsciiSlugger.php',
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => $vendorDir . '/symfony/string/Slugger/SluggerInterface.php',
    'Symfony\\Component\\String\\UnicodeString' => $vendorDir . '/symfony/string/UnicodeString.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Intl\\Grapheme\\Grapheme' => $vendorDir . '/symfony/polyfill-intl-grapheme/Grapheme.php',
    'Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Normalizer.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
