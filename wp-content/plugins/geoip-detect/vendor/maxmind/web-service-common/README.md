# Common Code for MaxMind Web Service Clients #

This is _not_ intended for direct use by third parties. Rather, it is for
shared code between MaxMind's various web service client APIs.

## Requirements  ##

The library requires PHP 5.6 or greater.

There are several other dependencies as defined in the `composer.json` file.

## Contributing ##

Patches and pull requests are encouraged. All code should follow the PSR-2
style guidelines. Please include unit tests whenever possible.

## Versioning ##

This API uses [Semantic Versioning](http://semver.org/).

## Copyright and License ##

This software is Copyright (c) 2015-2020 by MaxMind, Inc.

This is free software, licensed under the Apache License, Version 2.0.
