!function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,c,u=[],a=!0,f=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;a=!1}else for(;!(a=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);a=!0);}catch(e){f=!0,o=e}finally{try{if(!a&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(f)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}e.r(t),e.d(t,{ConditionalWrapper:function(){return o},FadeIn:function(){return a},JSXFromText:function(){return y},SprintR:function(){return b},create:function(){return M},getComponents:function(){return v},useFocusTrap:function(){return H},useScript:function(){return P},useStateWithDep:function(){return J}});var o=function(e){var t=e.condition,r=e.wrapper,n=e.children;return t?r(n):n},i=gform.libraries,c=i.React.useState,u=i.React.useEffect;function a(e){var t=n(c(0),2),r=t[0],o=t[1],a="number"==typeof e.transitionDuration?e.transitionDuration:400,f="number"==typeof e.transformDistance?e.transformDistance:20,l="number"==typeof e.delay?e.delay:50,s=e.wrapperTag||"div",d=e.childTag||"div",p=void 0===e.visible||e.visible;return u((function(){var t=i.React.Children.count(e.children);if(p||(t=0),t===r){var n=setTimeout((function(){e.onComplete&&e.onComplete()}),a);return function(){return clearTimeout(n)}}var c=t>r?1:-1,u=setTimeout((function(){o(r+c)}),l);return function(){return clearTimeout(u)}}),[i.React.Children.count(e.children),l,r,p,a]),i.React.createElement(s,{className:e.className},i.React.Children.map(e.children,(function(t,n){return i.React.createElement(d,{className:e.childClassName,style:{transition:"opacity ".concat(a,"ms, transform ").concat(a,"ms"),transform:r>n?"none":"translateY(".concat(f,"px)"),opacity:r>n?1:0}},t)})))}function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function l(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:String(t)}function s(e,t,r){return(t=l(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t){return Object.keys(e).reduce((function(r,n){var o=t[n]||{};return r[n]={component:o.component||e[n],props:p({$style:o.style},o.props)},r}),{})}function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},m.apply(this,arguments)}function y(e){var t,r,n,o,c=e.closingSymbol,u=void 0===c?"%%":c,a=e.openingSymbol,f=void 0===a?"%%":a,l=e.text,s=void 0===l?"":l,d=e.tokens,p=(void 0===d?[]:d).reduce((function(e,t){return e[t.key]=t,e}),{});return i.React.createElement(i.React.Fragment,null,(t=s,r=new RegExp("".concat(f,"(.*?)").concat(u,"([\\s\\S]*?)").concat(f,"\\1").concat(u),"g"),n=0,o=[],t.replace(r,(function(e,r,c,u){o.push(t.slice(n,u));var a=p[r];if(a){var f=a.component;o.push(i.React.createElement(f,m({},a.props,{key:"".concat(r,"-").concat(u)}),c))}else o.push(c);n=u+e.length})),o.push(t.slice(n)),o))}function b(e){var t=e.text,r=void 0===t?"":t,n=e.tokens,o=void 0===n?[]:n;return i.React.createElement(i.React.Fragment,null,r.split(/(%\d\$s)/).map((function(e,t){var r=e.match(/%(\d)\$s/);if(r){var n=parseInt(r[1],10)-1,c=o[n];if(!c)return null;var u=c.component;return i.React.createElement(u,m({key:t},c.props))}return e})))}function g(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var h=["src","checkForExisting"],O=i.React.useState,w=i.React.useEffect,S="undefined"!=typeof window&&void 0!==window.document,j={},E=function(e){var t=document.querySelector('script[src="'.concat(e,'"]'));if(t)return j[e]={loading:!1,error:null,scriptEl:t}};function P(e){var t=e.src,r=e.checkForExisting,o=void 0!==r&&r,i=g(e,h),c=t?j[t]:void 0;!c&&o&&t&&S&&(c=E(t));var u=n(O(c?c.loading:Boolean(t)),2),a=u[0],f=u[1],l=n(O(c?c.error:null),2),s=l[0],d=l[1];return w((function(){if(S&&t&&a&&!s){var e;!(c=j[t])&&o&&(c=E(t)),c?e=c.scriptEl:((e=document.createElement("script")).src=t,Object.keys(i).forEach((function(t){void 0===e[t]?e.setAttribute(t,i[t]):e[t]=i[t]})),c=j[t]={loading:!0,error:null,scriptEl:e});var r=function(){c&&(c.loading=!1),f(!1)},n=function(e){c&&(c.error=e),d(e)};return e.addEventListener("load",r),e.addEventListener("error",n),document.body.appendChild(e),function(){e.removeEventListener("load",r),e.removeEventListener("error",n)}}}),[t]),[a,s]}var R=gform.utils,A=/input|select|textarea|button|object/,k="a, input, select, textarea, button, object, [tabindex]";function D(e){var t=e.getAttribute("tabindex");return null===t&&(t=void 0),parseInt(t,10)}function F(e){var t=e.nodeName.toLowerCase(),r=!isNaN(D(e));return(A.test(t)&&!e.disabled||e instanceof HTMLAnchorElement&&e.href||r)&&function(e){for(var t,r=e;r&&r!==document.body;){if(t=r,!(0,R.isJestTest)()&&(t.offsetWidth<=0&&t.offsetHeight<=0||"none"===t.style.display))return!1;r=r.parentNode}return!0}(e)}function T(e){var t=D(e);return(isNaN(t)||t>=0)&&F(e)}function x(e){return Array.from(e.querySelectorAll(k)).filter(T)}var L=i.React.useCallback,C=i.React.useEffect,N=i.React.useRef;var H=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=N(),n=N(null),o=function(){var e=[],t=null,r=!1;function n(){r=!0}function o(){r&&(r=!1,t&&(t.contains(document.activeElement)||(x(t)[0]||t).focus()))}return{markForFocusLater:function(){e.push(document.activeElement)},returnFocus:function(){var t=null;try{(t=e.pop())&&setTimeout((function(){return t.focus()}))}catch(e){}},setupScopedFocus:function(e){(t=e).addEventListener("focusout",n,!1),t.addEventListener("focusin",o,!0)},teardownScopedFocus:function(){t.removeEventListener("focusout",n),t.removeEventListener("focusin",o),t=null}}}(),i=o.markForFocusLater,c=o.returnFocus,u=o.setupScopedFocus,a=o.teardownScopedFocus,f=L((function(o){if(n.current&&n.current(),r.current&&(c(),a()),e&&o){u(o),i();var f=function(e){n.current=t.disableAriaHider?null:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"body > :not(script)",r=Array.from(document.querySelectorAll(t)).map((function(t){if(!t.contains(e)){var r=t.getAttribute("aria-hidden");return null!==r&&"false"!==r||t.setAttribute("aria-hidden","true"),{node:t,ariaHidden:r}}}));return function(){r.forEach((function(e){e&&(null===e.ariaHidden?e.node.removeAttribute("aria-hidden"):e.node.setAttribute("aria-hidden",e.ariaHidden))}))}}(e);var r=null;if(t.focusSelector&&(r="string"==typeof t.focusSelector?e.querySelector(t.focusSelector):t.focusSelector),!r){var o=Array.from(e.querySelectorAll(k));!(r=o.find(T)||o.find(F)||null)&&F(e)&&(r=e)}r&&r.focus()};setTimeout((function(){o.ownerDocument&&f(o)})),r.current=o}else r.current=null}),[e,t.focusSelector,t.disableAriaHider]);return C((function(){if(e){var t=function(e){"Tab"===e.key&&r.current&&function(e,t){var r=x(e);if(r.length){if(r[t.shiftKey?0:r.length-1]===document.activeElement||e===document.activeElement){t.preventDefault();var n=r[t.shiftKey?r.length-1:0];n&&n.focus()}}else t.preventDefault()}(r.current,e)};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}}),[e]),f};function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var M=function(e,t){var r=(0,i.zustand)((function(r){return q(q({},e),t(r))}));return r.getData=function(){var t=r.getState();return(0,R.filterObject)(t,(function(t){var r=n(t,1)[0];return void 0!==e[r]}))},r.getActions=function(){var e=r.getState(),o=t();return(0,R.filterObject)(e,(function(e){var t=n(e,1)[0];return void 0!==o[t]}))},r},$=i.React.useState,W=i.React.useEffect;function J(e){var t=n($(e),2),r=t[0],o=t[1];return W((function(){o(e)}),[e]),[r,o]}window.gform=window.gform||{},window.gform.utils=window.gform.utils||{},window.gform.utils.react=window.gform.utils.react||{};var K;K=window.gform.utils.react,Object.entries(t).forEach((function(e){var t=n(e,2),r=t[0],o=t[1];K[r]=o}))}();