"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[528],{2389:function(t,e,o){o.r(e),o.d(e,{default:function(){return h}});var i,n=o(5518),a=o(2340),c=o.n(a),d=o(1024),s=o.n(d),r=o(1519),l=o.n(r),g=o(7329),m=o.n(g),u=o(9608),f=o.n(u),b=o(2036),v=(null===m()||void 0===m()||null===(i=m().components)||void 0===i?void 0:i.editor_button)||{},p=function(t,e){var o=v.endpoints.save_editor_settings.action,i=v.endpoints.save_editor_settings.nonce,n=v.form;(0,b.v_)({endpoint:f()+"?action="+o,body:{form:n,name:t,value:e,_ajax_nonce:i}})},w=function(){var t,e,o,i,a,d;c().instances=c().instances||{},c().instances.editorbutton={},c().instances.editorbutton.compactToggle={},c().instances.editorbutton.idToggle={},t=v.i18n,e=t.title,o=t.closeButtonAriaLabel,c().instances.editorbutton.flyout=new(l())({id:"editor-button-flyout",title:(0,n.escapeHtml)(e),triggers:'[data-js="editor-flyout-trigger"]',closeButtonTitle:o,closeButtonAriaLabel:o,closeButtonClasses:"gform-button--circular",closeButtonSize:"xs",closeButtonType:"simplified",wrapperClasses:"gform-dialog gform-dialog--editor-button"}),function(){var t=v.i18n,e=t.compactToggleLabel,o=t.compactToggleText;c().instances.editorbutton.compactToggle=new(s())({customAttributes:{"data-js":"editor-flyout-toggle"},id:"editor-button-flyout-toggle-compact",icons:!0,label:e,labelPosition:"right",labelVisible:!0,name:e,target:'[data-js="gform-dialog-content"]',targetPosition:"beforeend",ariaDescribedby:"editor-button-flyout-toggle-compact-description",initialChecked:v.compactViewEnabled,theme:"cosmos"});var i='<div class="gform-dialog__content-toggle-text" id="editor-button-flyout-toggle-compact-description"> '.concat(o," </div>");(0,n.getNode)("gform-dialog-content").insertAdjacentHTML("beforeend",i)}(),function(){var t=v.i18n,e=t.idToggleLabel,o=t.idToggleText;c().instances.editorbutton.idToggle=new(s())({customAttributes:{"data-js":"editor-flyout-toggle"},customClasses:["gform-toggle-id"],id:"editor-button-flyout-toggle-id",icons:!0,label:e,labelPosition:"right",labelVisible:!0,name:e,target:'[data-js="gform-dialog-content"]',targetPosition:"beforeend",ariaDescribedby:"editor-button-flyout-toggle-id-description",disabled:!v.compactViewEnabled,initialChecked:v.fieldIdEnabled});var i='<div class="gform-dialog__content-toggle-text" id="editor-button-flyout-toggle-id-description"> '.concat(o," </div>");(0,n.getNode)("gform-dialog-content").insertAdjacentHTML("beforeend",i)}(),i=c().instances.editorbutton.compactToggle.elements.input,a=c().instances.editorbutton.idToggle.elements.input,d=(0,n.getNode)("form-editor"),i.checked?(d.classList.add("gform-compact-view"),(0,n.trigger)({event:"gform/form_editor/compact-view-active",native:!1})):(0,n.trigger)({event:"gform/form_editor/compact-view-inactive",native:!1}),i.addEventListener("change",(function(){i.checked?(v.compactViewEnabled=!0,d.classList.add("gform-compact-view"),a.disabled=!1,(0,n.trigger)({event:"gform/form_editor/compact-view-active",native:!1})):(d.classList.remove("gform-compact-view"),v.compactViewEnabled=!1,a.disabled=!0,(0,n.trigger)({event:"gform/form_editor/compact-view-inactive",native:!1})),p("compact_view",i.checked?"enable":"disable")})),function(){var t=c().instances.editorbutton.idToggle.elements.input,e=(0,n.getNode)("form-editor");t.checked&&e.classList.add("gform-compact-view--show-id"),t.addEventListener("change",(function(){t.checked?(v.fieldIdEnabled=!0,e.classList.add("gform-compact-view--show-id")):(v.fieldIdEnabled=!1,e.classList.remove("gform-compact-view--show-id")),p("compact_view_show_id",t.checked?"enable":"disable")}))}()},h=function(){w(),(0,n.consoleInfo)("Gravity Forms Admin: Initialized all editor button scripts.")}}}]);