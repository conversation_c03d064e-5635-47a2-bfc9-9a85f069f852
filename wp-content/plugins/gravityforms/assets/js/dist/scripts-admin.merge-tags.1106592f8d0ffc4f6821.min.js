"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[514],{1589:function(e,t,i){i.r(t),i.d(t,{default:function(){return te}});var n,r,a,o,l,s,d,c,u,p,g,f,m,v=i(1236),b=i(2340),h=i.n(b),y=i(7329),_=i.n(y),w=i(5311),O=i(191),j=i.n(O),L=i(5518),T=i(107),A=i(7536),x=i.n(A),C=(null===(n=window)||void 0===n?void 0:n.form)||{},P=(null===(r=window)||void 0===r?void 0:r.GetInputType)||null,E=(null===(a=window)||void 0===a?void 0:a.<PERSON>)||null,I=(null===(o=window)||void 0===o?void 0:o.GetInput)||null,F=(null===(l=window)||void 0===l?void 0:l.Copy)||null,k=(null===(s=window)||void 0===s?void 0:s.IsPricingField)||null,q=(null===(d=window)||void 0===d?void 0:d.HasPostField)||null,S=function(e,t){var i=e.classList.value;if(!i)return"";var n=i.split(" ");for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var a=n[r].split("-");if("mt"==a[0]&&a[1]==t)return a.length>3?(delete a[0],delete a[1],a):2===a.length||a[2]}return""},D=function(e){for(var t in x().mergeTags)if(Object.prototype.hasOwnProperty.call(x().mergeTags,t)){var i=x().mergeTags[t].tags;for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)&&i[n].tag==e)return i[n].label}return""},z=function(e){return x().mergeTags[e].label},B=function(e,t){void 0===t&&(t="");var i=[],n=P(e),r="list"===n?":"+t:"",a="",o="",l="";if(w.inArray(n,["date","email","time","password"])>-1&&(l=e.inputs,e.inputs=null),void 0!==e.inputs&&w.isArray(e.inputs)){for(var s in"checkbox"===n&&(a="{"+(o=E(e,e.id).replace("'","\\'"))+":"+e.id+r+"}",i.push({tag:a,label:o})),e.inputs)if(Object.prototype.hasOwnProperty.call(e.inputs,s)){var d=e.inputs[s];"creditcard"===n&&w.inArray(parseFloat(d.id),[parseFloat(e.id+".2"),parseFloat(e.id+".3"),parseFloat(e.id+".5")])>-1||(a="{"+(o=E(e,d.id).replace("'","\\'"))+":"+d.id+r+"}",i.push({tag:a,label:o}))}}else a="{"+(o=E(e).replace("'","\\'"))+":"+e.id+r+"}",i.push({tag:a,label:o});return w.inArray(n,["date","email","time","password"])>-1&&(e.inputs=l),i},H=function(e,t,i,n,r,a){void 0===e&&(e=[]),void 0===n&&(n=[]);var o=[],l=[],s=[],d=[],c=[],u=[],p=[],g=[],f=[];if(i||s.push({tag:"{all_fields}",label:D("{all_fields}")}),!r){for(var m in e)if(Object.prototype.hasOwnProperty.call(e,m)){var v=e[m];if(!v.displayOnly){var b=P(v);if(-1===w.inArray(b,n)){if(v.isRequired)if("name"===b){var y=F(v),_=void 0,O=void 0,j=void 0,L=void 0;"extended"===v.nameFormat?(_=I(v,v.id+".2"),j=I(v,v.id+".8"),(L=F(v)).inputs=[_,j],l.push(L),delete y.inputs[0],delete y.inputs[3]):"advanced"===v.nameFormat&&(_=I(v,v.id+".2"),O=I(v,v.id+".4"),j=I(v,v.id+".8"),(L=F(v)).inputs=[_,O,j],l.push(L),delete y.inputs[0],delete y.inputs[2],delete y.inputs[4]),o.push(y)}else o.push(v);else l.push(v);k(v.type)&&p.push(v)}}}if(o.length>0)for(var T in o)Object.prototype.hasOwnProperty.call(o,T)&&(g=g.concat(B(o[T],a)));if(l.length>0)for(var A in l)Object.prototype.hasOwnProperty.call(l,A)&&"submit"!==l[A].type&&(f=f.concat(B(l[A],a)));if(p.length>0)for(var C in i||d.push({tag:"{pricing_fields}",label:D("{pricing_fields}")}),p)Object.prototype.hasOwnProperty.call(p,C)&&d.concat(B(p[C],a))}var E=["ip","date_mdy","date_dmy","embed_post:ID","embed_post:post_title","embed_url","entry_id","entry_url","form_id","form_title","user_agent","referer","post_id","post_edit_url","user:display_name","user:user_email","user:user_login"];for(var S in r&&(E.splice(E.indexOf("entry_id"),1),E.splice(E.indexOf("entry_url"),1),E.splice(E.indexOf("form_id"),1),E.splice(E.indexOf("form_title"),1)),q()&&!r||(E.splice(E.indexOf("post_id"),1),E.splice(E.indexOf("post_edit_url"),1)),E)-1===w.inArray(E[S],n)&&c.push({tag:"{"+E[S]+"}",label:D("{"+E[S]+"}")});var H=function(){for(var e in x().mergeTags)if(Object.prototype.hasOwnProperty.call(x().mergeTags,e)&&"custom"===e)return x().mergeTags[e];return[]}();if(H.tags.length>0)for(var G in H.tags)if(Object.prototype.hasOwnProperty.call(H.tags,G)){var V=H.tags[G];u.push({tag:V.tag,label:V.label})}var N={ungrouped:{label:z("ungrouped"),tags:s},required:{label:z("required"),tags:g},optional:{label:z("optional"),tags:f},pricing:{label:z("pricing"),tags:d},other:{label:z("other"),tags:c},custom:{label:z("custom"),tags:u}};return N=h().applyFilters("gform_merge_tags",N,t,i,n,r,a,undefined)},G=function(e){var t="number"===e.type&&e.enableCalculation,i="product"===e.type&&"calculation"===e.inputType&&e.enableCalculation,n="post_custom_field"===e.type&&"number"===e.inputType;return t||i||n},V=function(e,t,i){var n=[],r=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return h().applyFilters("gform_merge_tags_supported_input_types",["text","select","number","checkbox","radio","hidden","singleproduct","price","hiddenproduct","calculation","singleshipping"],e,t,i)}(e,t,i);return t.forEach((function(e){if(e.inputType||(e.inputType=e.type),r.includes(e.inputType))if("checkbox"===e.inputType&&e.choices){var t=1;e.choices.forEach((function(i){n.push({value:"{".concat(i.text,":").concat(e.id,".").concat(t,"}"),label:i.text});do{t++}while(t%10==0)}))}else"product"===e.type&&e.inputs?e.inputs.forEach((function(t){n.push({value:"{".concat(e.label," (").concat(t.label,"):").concat(t.id,"}"),label:"".concat(e.label," (").concat(t.label,")")})})):n.push({value:"{".concat(e.label,":").concat(e.id,"}"),label:e.label})})),h().applyFilters("gform_custom_merge_tags",n,e,t,i)},N=function(e){var t=C.fields,i=e.getAttribute("id"),n=e.classList.contains("merge-tag-calculation"),r=t.some((function(e){return G(e)})),a=[];if(n&&r)return V(C.id,t,i);n&&(c=new Event("gform/layout_editor/merge_tags_first_load"),document.dispatchEvent(c));var o=!0===S(e,"hide_all_fields"),l=S(e,"exclude"),s=S(e,"prepopulate"),d=S(e,"option");s&&(o=!0);var u=H(t,i,o,l,s,d),p=function(e){var t=0;for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&e[i].tags.length>0&&t++;return t>1}(u);for(var g in u)if(Object.prototype.hasOwnProperty.call(u,g)){var f=u[g].label,m=u[g].tags,v=f&&p;if(!(m.length<=0)){var b=m.map((function(e){return{value:e.tag,label:h().tools.stripSlashes(e.label)}}));v?a.push({label:f,listData:b}):a.push.apply(a,(0,T.Z)(b))}}return a},Z=(null===_()||void 0===_()||null===(u=_().components)||void 0===u?void 0:u.merge_tags)||{},M=(null===(p=window)||void 0===p?void 0:p.InsertVariable)||null,R=(null===(g=window)||void 0===g?void 0:g.InsertEditorVariable)||null,J=(null===(f=window)||void 0===f?void 0:f.form)||{},K=function(e){this.isEditor?R(this.elem.getAttribute("id"),e):M(this.elem.getAttribute("id"),null,e),w(this.elem).trigger("input").trigger("propertychange")},Q=function(){if(h().simplebar.initializeInstances(),function(e){var t=document.querySelector('[data-js="'.concat(e,'"]')),i=(0,L.getClosest)(t,".panel-block-tabs__body");if(i){var n=250-i.offsetHeight,r=window.getComputedStyle(i).getPropertyValue("padding-bottom");n<10||(i.setAttribute("data-js-initial-padding",r),i.style.paddingBottom="".concat(n,"px"))}}(this.selector),(0,L.browsers)().firefox){var e=document.querySelector('[data-js="'.concat(this.selector,'"]'));e.querySelector(".gform-dropdown__container").removeAttribute("style"),L.simpleBar.reInitChildren(e)}},U=function(){var e,t,i;e=this.selector,t=document.querySelector('[data-js="'.concat(e,'"]')),(i=(0,L.getClosest)(t,".panel-block-tabs__body"))&&i.hasAttribute("data-js-initial-padding")&&(i.style.paddingBottom=i.getAttribute("data-js-initial-padding"),i.removeAttribute("data-js-initial-padding"))},W=function(e,t){var i=N(e),n=S(e,"manual_position"),r=n?function(e){var t=(0,L.getClosest)(e,".wp-editor-wrap").querySelector(".wp-media-buttons");return(0,L.getChildren)(t).slice(-1).pop()}(e):e,a=function(e,t){var i=S(e,"manual_position"),n=document.createElement("span");return n.classList.add("all-merge-tags"),n.classList.add("gform-merge-tags-dropdown-wrapper"),n.classList.add(e.tagName.toLowerCase()),i?n.classList.add("left"):n.classList.add("right"),n.setAttribute("mt-dropdown-".concat(t),!0),n.innerHTML='<span data-js="gform-dropdown-mt-wrapper-'.concat(t,'"></span>'),n}(e,t);(0,L.insertAfter)(a,r),h().instances.mergeTags.push(new(j())({attributes:'data-js-input-id="'.concat(e.getAttribute("id"),'"'),container:"mt-dropdown-".concat(t),selector:"gform-dropdown-mt-".concat(t),renderTarget:'[data-js="gform-dropdown-mt-wrapper-'.concat(t,'"]'),swapLabel:!1,listData:i,render:!0,triggerPlaceholder:(0,L.saferHtml)(m||(m=(0,v.Z)(['<i class="gform-icon gform-icon--merge-tag gform-button__icon"></i>']))),triggerTitle:Z.i18n.insert_merge_tags,wrapperClasses:"gform-dropdown gform-dropdown--merge-tags",triggerId:"mt-dropdown--trigger-".concat(t),triggerAriaId:"mt-dropdown--trigger-label-".concat(t),triggerClasses:"ui-state-disabled",onItemSelect:K.bind({isEditor:n,idx:t,elem:e}),searchPlaceholder:Z.i18n.search_merge_tags,onOpen:Q.bind({selector:"gform-dropdown-mt-".concat(t)}),onClose:U.bind({selector:"gform-dropdown-mt-".concat(t)}),dropdownListAttributes:'data-js="gform-simplebar"',hasSearch:!r.classList.contains("merge-tag-calculation")}))},X=function(){(0,L.getNodes)(".merge-tag-support:not(.mt-initialized)",!0,document,!0).forEach((function(e){var t=(0,L.uniqueId)();W(e,t),function(e){var t=(0,L.getClosest)(e,".field_setting"),i=(0,L.getClosest)(e,".gform-settings-field");t?t.classList.add("field_setting--with-merge-tag"):i&&i.classList.add("gform-settings-field--with-merge-tag")}(e),e.classList.add("mt-initialized")}))},Y=function(){var e,t=J.fields,i=$(),n=(null==i||null===(e=i.elements)||void 0===e?void 0:e.container)||null,r=n?n.getAttribute("data-js-input-id"):null,a=t.some((function(e){return G(e)})),o=V(J.id,t,r);a&&(i.options.listData=o,i.renderListData())},$=function(){var e=document.getElementById("calculation_options");if(!e)return null;for(var t=0;t<h().instances.mergeTags.length;t++){var i=h().instances.mergeTags[t],n=i.elements.container;if(e.contains(n))return i}return null},ee=function(){h().instances=(null===h()||void 0===h()?void 0:h().instances)||{},h().instances.mergeTags=h().instances.mergeTags||[],h().components=(null===h()||void 0===h()?void 0:h().components)||{},h().components.Dropdown=j(),document.addEventListener("gform/merge_tag/initialize",X),document.addEventListener("gform/form_editor/toggle_calculation_options",Y),document.addEventListener("gform/layout_editor/field_modified",Y),document.addEventListener("gform/form_editor/set_field_label",Y),document.addEventListener("gform/layout_editor/gform_field_deleted",Y),X()},te=function(){ee()}}}]);