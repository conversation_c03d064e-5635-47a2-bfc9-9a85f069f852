"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[356],{2142:function(e,t,a){a.r(t);var n=a(8349),i=a(5872),s=a.n(i),c=a(564),r=a.n(c),l=a(4216),o=a.n(l),u=a(405),m=a.n(u),d=a(5595),g=a.n(d),p=a(9645),f=a.n(p),h=a(6172),z=a.n(h),_=a(1047),w=a(6134);t.default=function(e){var t=e.data,a=e.i18n,i=(0,_.default)((function(e){return e.licenseKey})).length>0,c=(0,_.default)((function(e){return e.hideLicense})),l=(0,_.default)((function(e){return e.activeStep})),u=(0,_.default)((function(e){return e.setHideLicense})),d=a.set_up_title,p=a.set_up_copy,h=a.for_client,x=a.hide_license,y=a.enable_updates,R=a.enable_updates_tag,C=a.enable_updates_locked,E=a.updates_recommended,b=a.which_currency,v=a.previous,k={ref:(0,w.useFocusTrap)(2===l),className:(0,n.classnames)({"gform-setup-wizard__screen":!0,"gform-setup-wizard__screen--step-2":!0}),"aria-hidden":2!==l};return n.React.createElement("div",k,n.React.createElement("div",{className:"gform-setup-wizard__content"},n.React.createElement(o(),{content:d,customClasses:["gform-typography--md-size-display-sm"],size:"display-xs",weight:"medium",spacing:{"":3,md:5},tagName:"h2"}),n.React.createElement(z(),{content:p,spacing:{"":5,md:8},size:"text-md",weight:"regular"}),n.React.createElement(o(),{content:y,customClasses:["gform-typography--md-text-size-xl"],spacing:3,size:"text-lg",tagName:"h3",weight:"medium"},!i&&n.React.createElement(m(),{customClasses:["gform-setup-wizard__feature-disabled-tag"],content:C,triangleTag:!0}),i&&n.React.createElement(m(),{customClasses:["gform-setup-wizard__feature-disabled-tag"],content:R,triangleTag:!0})),n.React.createElement(z(),{content:E,size:"text-sm",weight:"regular",spacing:3}),n.React.createElement(g(),{size:"size-l",disabled:!i,initialChecked:(0,_.default)((function(e){return e.autoUpdate})),onChange:(0,_.default)((function(e){return e.setAutoUpdate})),spacing:{"":6,md:8},ariaLabel:y}),n.React.createElement(o(),{content:b,customClasses:["gform-typography--md-text-size-xl"],size:"text-lg",spacing:{"":3,md:5},weight:"medium"}),n.React.createElement(s(),{customClasses:["gform-setup-wizard__currency-container"],spacing:{"":6,md:8}},n.React.createElement(f(),{ariaLabel:b,initialValue:(0,_.default)((function(e){return e.currency})),onChange:(0,_.default)((function(e){return e.setCurrency})),options:t.options.currencies,size:"size-xl"})),i&&n.React.createElement(n.React.Fragment,null,n.React.createElement(o(),{content:h,customClasses:["gform-typography--md-text-size-xl"],spacing:3,size:"text-lg",tagName:"h3",weight:"medium"}),n.React.createElement(z(),{content:x,spacing:3,size:"text-sm",weight:"regular"}),n.React.createElement(g(),{size:"size-l",initialChecked:c,onChange:u,spacing:{"":6,md:8},ariaLabel:x})),n.React.createElement(s(),{x:850,customClasses:["gform-setup-wizard__footer"],display:"flex"},!t.options.hasLicense&&n.React.createElement(r(),{size:"size-height-xl",type:"white",icon:"arrow-narrow-left",iconPrefix:"gform-common-icon",onClick:(0,_.default)((function(e){return e.setActiveStepPrevious})),ariaLabel:v}),n.React.createElement(r(),{size:"size-height-xl",customClasses:["gform-setup-wizard__nav-next-alt"],label:"Next",icon:"arrow-narrow-right",iconPrefix:"gform-common-icon",iconPosition:"trailing",onClick:(0,_.default)((function(e){return e.setActiveStepNext}))}))))}}}]);