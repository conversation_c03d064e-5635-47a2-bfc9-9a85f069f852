"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[200],{5475:function(e,t,r){r.r(t);var n=r(6796),o=r(7063),a=r(8349),i=r(5518),c=r(5872),l=r.n(c),u=r(564),s=r.n(u),f=r(4824),m=r.n(f),d=r(5211),p=r.n(d),y=r(5235),b=r.n(y),g=r(3854),v=r(89),h=r.n(v),E=r(8665);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach((function(t){(0,o.Z)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var C=a.React.useCallback,R=a.React.useEffect,P=a.React.useRef;t.default=function(e){var t=e.flyoutAttributes,r=void 0===t?{}:t,o=e.footerAttributes,c=void 0===o?{}:o,u=e.showAlert,f=void 0!==u&&u,d=e.strings,y=void 0===d?{}:d,v=e.titleInputState,O=void 0===v?{}:v,_=(0,E.default)((function(e){return e.flyoutTitleValue})),k=(0,E.default)((function(e){return e.setFlyoutTitleValue})),A=(0,E.default)((function(e){return e.flyoutDescriptionValue})),j=(0,E.default)((function(e){return e.setFlyoutDescriptionValue})),x=(0,E.default)((function(e){return e.flyoutOpen})),D=P(null);R((function(){x&&D.current&&setTimeout((function(){var e=Array.from(D.current.children).find((function(e){return"INPUT"===e.tagName}));e&&e.focus()}),100)}),[x]),R((function(){var e=function(e){if("Enter"===e.key&&x&&D.current){var t=Array.from(D.current.children).find((function(e){return"INPUT"===e.tagName}));t===document.activeElement&&N.current&&(e.preventDefault(),k(t.value),setTimeout((function(){N.current()}),100))}};return document.addEventListener("keydown",e),function(){document.removeEventListener("keydown",e)}}));var T={className:(0,a.classnames)({"gform-flyout__footer":!0})},z=w(w({},c.primaryButtonAttributes),{},{customClasses:(0,a.classnames)({"gform-flyout__footer-primary-button":!0}),type:"primary-new"}),N=P(z.onClick);N.current=z.onClick;var I=w(w({},c.secondaryButtonAttributes),{},{customClasses:(0,a.classnames)({"gform-flyout__footer-secondary-button":!0}),type:"white"});r.afterContent=a.React.createElement("footer",T,a.React.createElement("div",{className:"gform-flyout__footer-inner"},a.React.createElement(s(),I),a.React.createElement(s(),z)));var B=C((0,i.debounce)(k,{wait:300}),[_]),F=C((0,i.debounce)(j,{wait:300}),[A]),S=w({closeButtonCustomAttributes:{icon:"x",iconPrefix:"gform-common-icon",size:"size-xs",type:"simplified"},customClasses:["gform-template-library__flyout"],direction:"right",desktopWidth:100,mobileBreakpoint:768,mobileWidth:100,headerHeadingCustomAttributes:{size:"display-sm",weight:"semibold"},zIndex:100001},r),V={controlled:!0,id:"template-library-form-title-input",required:!0,requiredLabel:{content:"(".concat(y.required,")")},placeholder:y.titlePlaceholder,size:"size-l",labelPosition:"above",labelAttributes:{label:y.title,htmlFor:"template-library-form-title-input"},onChange:B,error:O.errorState,helpTextAttributes:{content:O.errorMessage},value:_};return a.React.createElement(h(),S,f&&a.React.createElement(g.Z,{content:y.upgradeAlert,contentCustomAttributes:{asHtml:!0},customClasses:["gform-template-library__flyout-alert"],isInline:!0,spacing:6,type:"error"}),a.React.createElement(l(),{spacing:6},a.React.createElement(m(),(0,n.Z)({},V,{ref:D}))),a.React.createElement(l(),{spacing:6},a.React.createElement(p(),{label:y.description,htmlFor:"template-library-form-description-text"}),a.React.createElement(b(),{controlled:!0,id:"template-library-form-description-text",placeholder:y.formDescriptionPlaceHolder,onChange:F,value:A,customClasses:["gform-template-library__flyout-textarea"]})))}}}]);