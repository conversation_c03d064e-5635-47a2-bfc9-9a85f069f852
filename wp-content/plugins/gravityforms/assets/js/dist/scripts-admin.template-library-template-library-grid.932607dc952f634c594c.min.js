"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[805],{8360:function(e,t,a){a.r(t);var r=a(107),l=a(8349),i=a(351),n=a.n(i),c=a(5718),s=a.n(c),m=a(7941),o=a.n(m),u=a(7390);t.default=function(e){var t,a,i,c=e.blankOnClick,m=void 0===c?function(){}:c,g=e.licenseType,p=void 0===g?"":g,d=e.strings,b=void 0===d?{}:d,f=e.templateOnClick,k=void 0===f?function(){}:f,v=e.templates,y=void 0===v?[]:v,h=e.thumbnailUrl,_=void 0===h?"":h;return l.React.createElement(l.<PERSON>,null,l.React.createElement("div",{className:"gform-template-library__card-grid-container"},l.React.createElement(n(),{container:!0,wrap:!0,rowSpacing:6,columnSpacing:6,customClasses:["gform-template-library__card-grid"],justifyContent:"flex-start"},(t={headingAttributes:{content:b.blankForm,weight:"medium",size:"text-sm",tagName:"h2"},textAttributes:{content:b.createForm,size:"text-sm"},blankButtonAttributes:{onClick:m},imageAttributes:{asBg:!0,url:"https://i.imgur.com/KsZxvrs.png",altText:b.blankForm},style:"form-template-blank"},a=l.React.createElement(n(),{key:0,customClasses:["gform-template-library__card-grid-item"],item:!0},l.React.createElement(o(),t)),i=y.map((function(e,t){var a=p&&p.slice(2).toLowerCase()||"single",r=(0,u.requiresUpgrade)({accessLevels:e.template_access_level,licenseLevel:a})?b.upgradeTag:"",i=b.useTemplateWithTitle.split("%s"),c=b.previewWithTitle.split("%s"),m={customClasses:["gform-card__form-template-secondary-button-icon"],icon:"external-link"},g={bgColor:e.template_background,headingAttributes:{content:e.title,weight:"medium",size:"text-sm",tagName:"h2"},primaryCtaAttrs:{ctaType:"button",children:l.React.createElement(l.React.Fragment,null,i[0],l.React.createElement("span",{className:"gform-visually-hidden"},'"'.concat(e.title,'"')),i[1]),onClick:k(e)},secondaryCtaAttrs:{ctaType:"link",children:l.React.createElement(l.React.Fragment,null,l.React.createElement(s(),m),c[0],l.React.createElement("span",{className:"gform-visually-hidden"},'"'.concat(e.title,'"')),c[1]),href:e.template_preview_url,target:"_blank"},imageAttributes:{asBg:!0,url:"".concat(_).concat(e.template_thumbnail),imagePosition:"top center",imageAttributes:{style:{backgroundSize:"100%"}},altText:e.title},tagAttributes:{content:r,size:"text-xxs"},style:"form-template"};return l.React.createElement(n(),{key:t+1,customClasses:["gform-template-library__card-grid-item"],item:!0},l.React.createElement(o(),g))})),[a].concat((0,r.Z)(i))))))}}}]);