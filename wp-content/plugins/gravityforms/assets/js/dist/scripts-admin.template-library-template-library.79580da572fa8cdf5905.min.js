"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[840],{4785:function(e,t,n){n.r(t);var r=n(9801),i=n(9509),l=n.n(i),a=n(8349),o=n(9843),u=n.n(o),s=n(5872),c=n.n(s),f=n(4216),d=n.n(f),m=n(6172),p=n.n(m),y=n(8360),b=n(5475),T=n(7390),g=n(8665),k=a.React.useEffect;t.default=function(e){var t=(0,g.default)((function(e){return e.flyoutOpen})),n=(0,g.default)((function(e){return e.setFlyoutOpen})),i=(0,g.default)((function(e){return e.flyoutFooterButtonLabel})),o=(0,g.default)((function(e){return e.setFlyoutFooter<PERSON>uttonLabel})),s=(0,g.default)((function(e){return e.flyoutTitleValue})),f=(0,g.default)((function(e){return e.setFlyoutTitleValue})),m=(0,g.default)((function(e){return e.flyoutDescriptionValue})),h=(0,g.default)((function(e){return e.setFlyoutDescriptionValue})),C=(0,g.default)((function(e){return e.selectedTemplate})),v=(0,g.default)((function(e){return e.setSelectedTemplate})),_=(0,g.default)((function(e){return e.flyoutTitleErrorState})),E=(0,g.default)((function(e){return e.setFlyoutTitleErrorState})),F=(0,g.default)((function(e){return e.flyoutTitleErrorMessage})),B=(0,g.default)((function(e){return e.setFlyoutTitleErrorMessage})),O=(0,g.default)((function(e){return e.importError})),x=(0,g.default)((function(e){return e.setImportError})),L=(0,g.default)((function(e){return e.flyoutPrimaryLoadingState})),w=(0,g.default)((function(e){return e.setFlyoutPrimaryLoadingState})),R=(0,g.default)((function(e){return e.bypassTemplateLibrary})),A=(0,g.default)((function(e){return e.isLibraryOpen})),V=(0,g.default)((function(e){return e.setIsLibraryOpen}));k((function(){document.addEventListener("gform/template_library/set_open_status",(function(e){V(e.detail.isOpen),e.detail.isOpen&&R&&n(!0)}))}),[]);var S=function(){var t=(0,r.Z)(l().mark((function t(){var n,r;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return D(),w(!0),t.next=4,(0,T.createForm)({template:C,title:s,description:m,endpoints:e.endpoints});case 4:(n=t.sent)&&n.error&&(-1!==n.error.code.indexOf("_title")?(E(!0),r="duplicate_title"===n.error.code?e.i18n.duplicateTitle:e.i18n.missingTitle,B(r)):x(!0),w(!1));case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),D=function(){E(!1),B("")},I={closeButtonType:"simplified",closeButtonTitle:e.i18n.closeButton,customCloseButtonClasses:["gform-template-library__exit-button"],customMaskClasses:t?["gform-template-library--flyout-open","gform-template-library__mask"]:["gform-template-library__mask"],customWrapperClasses:["gform-template-library"],id:"gform-template-library",isOpen:A,lockBody:!0,maskBlur:!1,maskTheme:"dark",mode:"modal",padContent:!1,zIndex:1e5,onClose:function(){V(!1)}},z={blankOnClick:function(){return D(),v({title:e.i18n.blankFormTitle,description:e.i18n.blankFormDescription,id:"blank"}),n(!0),void o(e.i18n.createForm)},licenseType:e.data.licenseType,strings:e.i18n,templateOnClick:function(t){return function(t){return function(){D(),v(t),n(!0),o(e.i18n.useTemplate)}}(t)},templates:e.data.templates,thumbnailUrl:e.data.thumbnail_url},M={flyoutAttributes:{isOpen:t,onClose:function(){f(""),h(""),n(!1),R&&V(!1)},simplebar:!0,title:null==C?void 0:C.title},titleInputState:{errorState:_,errorMessage:F},footerAttributes:{primaryButtonAttributes:{label:i,onClick:S,active:L,activeType:"loader",activeText:e.i18n.createActiveText},secondaryButtonAttributes:{label:e.i18n.cancel,onClick:function(){n(!1)}}},setFlyoutTitleValue:f,setFlyoutDescriptionValue:h,flyoutTitleValue:s,flyoutDescriptionValue:m,showAlert:"blank"!==C.id&&(0,T.requiresUpgrade)({accessLevels:C.template_access_level,licenseLevel:e.data.licenseType&&e.data.licenseType.slice(2).toLowerCase()||"single"}),strings:e.i18n};return a.React.createElement(u(),I,a.React.createElement(c(),{customClasses:["gform-template-library__heading"]},a.React.createElement(d(),{size:"display-sm",weight:"semibold",spacing:3,content:e.i18n.heading}),a.React.createElement(p(),{content:e.i18n.subheading,size:"text-md",weight:"regular"})),!R&&a.React.createElement(y.default,z),a.React.createElement(b.default,M),O&&a.React.createElement(u(),{alertButtonText:e.i18n.importErrorCloseText,buttonWidth:"full",confirmButtonType:"white",content:e.i18n.failedRequest,customWrapperClasses:["gform-template-library__alert"],isOpen:!0,maskBlur:!0,maskTheme:"dark",mode:"alert",onCloseAfterAnimation:function(){return x(!1)},showCloseButton:!1,theme:"cosmos",title:e.i18n.failedRequestDialogTitle,titleIndicatorType:"error",zIndex:100001}))}}}]);