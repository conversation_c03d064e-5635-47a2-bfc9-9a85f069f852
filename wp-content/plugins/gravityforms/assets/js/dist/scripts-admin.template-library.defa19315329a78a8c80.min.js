"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[236],{1786:function(e,t,r){r.r(t);var a,o,n=r(8349),i=r(7329),l=r.n(i),d=r(5518),u=r(4785),c=n.ReactDOM.createRoot,m=(null===l()||void 0===l()||null===(a=l().components)||void 0===a?void 0:a.template_library)||{},s=(null===l()||void 0===l()||null===(o=l().apps)||void 0===o?void 0:o.template_library)||{},p={templateLibraryTrigger:(0,d.getNode)("gform-add-new-form"),root:(0,d.getNode)(s.root_element)},f=function(){(0,d.trigger)({event:"gform/template_library/set_open_status",el:document,data:{isOpen:!0},native:!1})};t.default=function(){c(p.root).render(n.React.createElement(u.default,m)),p.templateLibraryTrigger.addEventListener("click",f)}}}]);